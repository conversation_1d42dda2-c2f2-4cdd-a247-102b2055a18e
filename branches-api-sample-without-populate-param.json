// Request URL: http://localhost:1339/api/branches
{
	"data": [
		{
			"id": 1,
			"title": "Calgary",
			"slug": "calgary",
			"address": "223 14 Street NW ",
			"city": "Calgary",
			"province": "alberta",
			"postal": "T2N 1Z6",
			"mapEmbedSrc": "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d63559730.7313724!2d-120.64714481286511!3d13.512372954476643!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x53716fea1ba82deb%3A0xa52b9002ec27e690!2sAxiom%20Mortgage%20Solutions!5e0!3m2!1sen!2sbr!4v",
			"mapLocationUrl": "https://goo.gl/maps/ajntTDbJQgCnQkNN6",
			"provinceLicenseNumber": null,
			"suiteUnit": null,
			"isCorporate": null,
			"createdAt": "2023-04-03T22:24:43.870Z",
			"updatedAt": "2023-04-20T13:17:43.390Z",
			"publishedAt": "2023-04-03T22:24:52.587Z",
			"documentId": "w4xd7w7mk3pjswtyuj86b07b"
		},
		{
			"id": 2,
			"title": "Winnipeg",
			"slug": "winnipeg",
			"address": "1525 Grant Ave",
			"city": "Winnipeg",
			"province": "manitoba",
			"postal": "R3N 0M3",
			"mapEmbedSrc": "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2572.2457585967445!2d-97.18820288702904!3d49.85662802982226!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x52ea745e41aeb8fb%3A0x2951c3907f13c18c!2s1525%20Grant%20Ave%2C%20Winnipeg%2C%20MB%20R3N%20",
			"mapLocationUrl": "https://www.google.com/maps/place/1525+Grant+Ave,+Winnipeg,+MB+R3N+0M3/@49.8566246,-97.1881975,17z/data=!3m1!4b1!4m6!3m5!1s0x52ea745e41aeb8fb:0x2951c3907f13c18c!8m2!3d49.8566246!4d-97.1856226!16s%2Fg%2F11kq2lfd7f?entry=ttu",
			"provinceLicenseNumber": null,
			"suiteUnit": null,
			"isCorporate": null,
			"createdAt": "2023-04-03T22:27:41.152Z",
			"updatedAt": "2024-09-06T20:30:48.288Z",
			"publishedAt": "2023-04-05T22:11:19.852Z",
			"documentId": "uud6fexxce26if8q1r1bm5xq"
		},
		{
			"id": 3,
			"title": "Victoria",
			"slug": "victoria",
			"address": "1152 Johnson Street",
			"city": "Victoria",
			"province": "britishColumbia",
			"postal": "V8V 3N8",
			"mapEmbedSrc": "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d63834884.887037955!2d-125.39856726154802!3d12.438246519425158!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x548f7489aeb00f09%3A0xecfac7d625ab01e9!2sJohnson%20St%2C%20Victoria%2C%20BC%20V8V%203N8%",
			"mapLocationUrl": "https://goo.gl/maps/dsSjtRMQkEnxmVj68",
			"provinceLicenseNumber": "029978",
			"suiteUnit": null,
			"isCorporate": null,
			"createdAt": "2023-04-19T22:00:49.542Z",
			"updatedAt": "2024-04-14T18:15:35.109Z",
			"publishedAt": "2023-04-19T22:01:36.838Z",
			"documentId": "or8p98zs3ztzs82r2pibri2x"
		},
		{
			"id": 4,
			"title": "Regina",
			"slug": "regina",
			"address": "3619 Pasqua Street",
			"city": " Regina",
			"province": "saskatchewan",
			"postal": "S4S 6W8",
			"mapEmbedSrc": "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d63627832.45913834!2d-115.94975444865406!3d13.254491728245517!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x531ea755909ec391%3A0xd11f9d148c31136d!2sPasqua%20St%2C%20Regina%2C%20SK%20S4S%206W8%2C%2",
			"mapLocationUrl": "https://goo.gl/maps/CchE5ubTjAZ5MNq69",
			"provinceLicenseNumber": "509361",
			"suiteUnit": null,
			"isCorporate": null,
			"createdAt": "2023-04-19T22:02:46.316Z",
			"updatedAt": "2024-04-14T18:20:42.045Z",
			"publishedAt": "2023-04-19T22:02:55.522Z",
			"documentId": "e7xx3j8j6kl7d5jzi8kio2ox"
		},
		{
			"id": 5,
			"title": "Vancouver",
			"slug": "vancouver",
			"address": "100 856 Homer Street",
			"city": " Vancouver",
			"province": "britishColumbia",
			"postal": "V6B 2W2",
			"mapEmbedSrc": "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2602.8709405130903!2d-123.12004742351127!3d49.27884267139212!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x5486717e74f7a495%3A0x127d49feb54dc2cd!2s856%20Homer%20St%20%23100%2C%20Vancouver%2C%20BC",
			"mapLocationUrl": "https://maps.app.goo.gl/Ev2ofFCYZZDHNLDZ9",
			"provinceLicenseNumber": "029978",
			"suiteUnit": null,
			"isCorporate": null,
			"createdAt": "2023-04-19T22:06:03.930Z",
			"updatedAt": "2024-10-12T03:01:24.112Z",
			"publishedAt": "2023-04-19T22:06:13.721Z",
			"documentId": "knj00rnsz5yjquu43x8tk1ac"
		},
		{
			"id": 6,
			"title": "Kelowna",
			"slug": "kelowna",
			"address": "1425 Ellis Street",
			"city": "Kelowna",
			"province": "britishColumbia",
			"postal": "V1Y 2A3",
			"mapEmbedSrc": "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d63684491.414778195!2d-123.40166475993972!3d13.036124729412055!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x537df4a7c1fd214b%3A0x337b11b1873b26db!2s1427%20Ellis%20St%2C%20Kelowna%2C%20BC%20V1Y%20",
			"mapLocationUrl": "https://goo.gl/maps/MWUEXCkUR4EWWJhTA",
			"provinceLicenseNumber": "029978",
			"suiteUnit": null,
			"isCorporate": null,
			"createdAt": "2023-04-19T22:07:52.015Z",
			"updatedAt": "2024-04-14T18:14:57.340Z",
			"publishedAt": "2023-04-19T22:08:12.395Z",
			"documentId": "zom7gqucwfuqlvtikr0qqenb"
		},
		{
			"id": 7,
			"title": "Mississauga",
			"slug": "mississauga",
			"address": "1 Hurontario St Suite 219 ",
			"city": "Mississauga",
			"province": "ontario",
			"postal": "L5G 0A3",
			"mapEmbedSrc": "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d64281144.36745149!2d-103.72320409199526!3d10.468333803427075!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x882b461329c56a1b%3A0xbc85d4c7d170398d!2s1%20Hurontario%20St%2C%20Mississauga%2C%20ON%20L",
			"mapLocationUrl": "https://goo.gl/maps/4dV5pvw9nGxkvAsX6",
			"provinceLicenseNumber": "12403",
			"suiteUnit": null,
			"isCorporate": null,
			"createdAt": "2023-04-19T22:09:37.658Z",
			"updatedAt": "2024-04-14T18:18:38.402Z",
			"publishedAt": "2023-04-19T22:09:46.760Z",
			"documentId": "qj2ddcjz1iba1g0y4ff01ein"
		},
		{
			"id": 8,
			"title": "London",
			"slug": "london",
			"address": " 400 B Central Avenue",
			"city": "London",
			"province": "ontario",
			"postal": "N6B 2E2",
			"mapEmbedSrc": "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d64327557.604176775!2d-104.57706460686327!3d10.24201333131001!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x882ef206dc7d6335%3A0x3c817365b0cb59ec!2sVictor%20Aziz%20Photography!5e0!3m2!1sen!2sbr!4v",
			"mapLocationUrl": "https://goo.gl/maps/fuzA3wF4b2AzA7Kw8",
			"provinceLicenseNumber": "12403",
			"suiteUnit": null,
			"isCorporate": null,
			"createdAt": "2023-04-20T10:54:39.558Z",
			"updatedAt": "2024-04-14T18:18:10.140Z",
			"publishedAt": "2023-04-20T10:54:48.409Z",
			"documentId": "ueii88onjbbvtpfynlfk79oo"
		},
		{
			"id": 9,
			"title": "Guelph",
			"slug": "guelph",
			"address": "412 Laird Road Unit 10",
			"city": " Guelph",
			"province": "ontario",
			"postal": "N1G 3X7",
			"mapEmbedSrc": "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d64286354.34576075!2d-104.03645979313896!3d10.443170597325999!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x882b848e73d33d89%3A0xc0a99a2b7ceb9dfb!2s412%20Laird%20Rd%2C%20Guelph%2C%20ON%20N1G%203X7",
			"mapLocationUrl": "https://goo.gl/maps/jqX8wwmhc7tCmViG7",
			"provinceLicenseNumber": "12403",
			"suiteUnit": null,
			"isCorporate": null,
			"createdAt": "2023-04-20T10:56:11.910Z",
			"updatedAt": "2024-04-14T18:16:33.374Z",
			"publishedAt": "2023-04-20T10:56:21.734Z",
			"documentId": "ubk6xslw2y9w7fk1roykwa9f"
		},
		{
			"id": 11,
			"title": "Tecumseh",
			"slug": "tecumseh",
			"address": "12122 Tecumseh RD E",
			"city": "Tecumseh",
			"province": "ontario",
			"postal": "N8N 1L9",
			"mapEmbedSrc": "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2950.433566905359!2d-82.8885768!3d42.3119504!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x883ad541cbf5856f%3A0x5e569f66b006786b!2s12122%20Tecumseh%20Rd%20E%2C%20Tecumseh%2C%20ON%20N8N%201L9%2C%2",
			"mapLocationUrl": "https://goo.gl/maps/2zn1NyDj3Utba46o9",
			"provinceLicenseNumber": "12403",
			"suiteUnit": null,
			"isCorporate": null,
			"createdAt": "2023-04-25T18:16:23.618Z",
			"updatedAt": "2024-04-14T18:18:59.802Z",
			"publishedAt": "2023-04-25T18:16:33.692Z",
			"documentId": "n2y66b9lj8w42k1xf69hjope"
		},
		{
			"id": 12,
			"title": "Leamington",
			"slug": "leamington",
			"address": "284 Erie Street S ",
			"city": "Leamington",
			"province": "ontario",
			"postal": "N8H 3C5",
			"mapEmbedSrc": "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2963.2892984716323!2d-82.6006208!3d42.0369764!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x883ac055f12df0e5%3A0xfc67a758bb35bd7!2s284%20Erie%20St%20S%2C%20Municipality%20Of%20Leamington%2C%20ON%",
			"mapLocationUrl": "https://goo.gl/maps/QDPHJYDAKTeziXkw8",
			"provinceLicenseNumber": "12403",
			"suiteUnit": null,
			"isCorporate": null,
			"createdAt": "2023-04-25T19:39:16.414Z",
			"updatedAt": "2024-04-14T18:17:18.852Z",
			"publishedAt": "2023-04-25T19:39:20.981Z",
			"documentId": "qhhicy07jycq3kqmf4v0skdp"
		},
		{
			"id": 13,
			"title": "Burlington",
			"slug": "burlington",
			"address": "184 Plains Road E",
			"city": "Burlington",
			"province": "ontario",
			"postal": "L4N8Y2",
			"mapEmbedSrc": "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2903.1493892990434!2d-79.84894732384122!3d43.311132071120454!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x882c9dc5e2cb1d13%3A0x1f043092b67a51e!2s184%20Plains%20Rd%20E%2C%20Burlington%2C%20ON%20L",
			"mapLocationUrl": "https://www.google.com/maps/place/184+Plains+Rd+E,+Burlington,+ON+L7T+2C3/@43.3111321,-79.8489473,17z/data=!3m1!4b1!4m6!3m5!1s0x882c9dc5e2cb1d13:0x1f043092b67a51e!8m2!3d43.3111321!4d-79.8463724!16s%2Fg%2F11b8vfbfr9?entry=ttu",
			"provinceLicenseNumber": "12403",
			"suiteUnit": null,
			"isCorporate": null,
			"createdAt": "2023-11-06T21:55:19.220Z",
			"updatedAt": "2024-04-14T18:14:07.762Z",
			"publishedAt": "2023-11-06T21:55:23.333Z",
			"documentId": "ztir92t9cj5qibkd6ojgb5vu"
		},
		{
			"id": 14,
			"title": "London",
			"slug": "london-1",
			"address": "112-611 Wonderland Rd. N ",
			"city": "London",
			"province": "ontario",
			"postal": "N6H1T6",
			"mapEmbedSrc": "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2918.8526793283313!2d-81.29213602385846!3d42.98137687114194!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x882ef1c971000001%3A0x7d17937ac6600d29!2s611%20Wonderland%20Rd%20N%20%23112%2C%20London%2C",
			"mapLocationUrl": "https://www.google.com/maps/place/611+Wonderland+Rd+N+%23112,+London,+ON+N6H+5N7/@42.9813769,-81.292136,17z/data=!3m1!4b1!4m6!3m5!1s0x882ef1c971000001:0x7d17937ac6600d29!8m2!3d42.9813769!4d-81.2895611!16s%2Fg%2F11sn00kynx?entry=ttu",
			"provinceLicenseNumber": "12403",
			"suiteUnit": null,
			"isCorporate": null,
			"createdAt": "2023-11-06T22:05:20.979Z",
			"updatedAt": "2024-04-14T18:17:39.628Z",
			"publishedAt": "2023-11-06T22:05:25.845Z",
			"documentId": "wm1ct7x9k13m8bwofeuep48k"
		},
		{
			"id": 15,
			"title": "Kitchener",
			"slug": "kitchener",
			"address": "240 Duke Street W",
			"city": "Kitchener",
			"province": "ontario",
			"postal": "N2H3X6",
			"mapEmbedSrc": "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2896.313726236668!2d-80.49800852383372!3d43.45404707111234!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x882bf48b1446f47f%3A0xd77855d158b6c278!2s240%20Duke%20St%20W%2C%20Kitchener%2C%20ON%20N2H%2",
			"mapLocationUrl": "https://www.google.com/maps/place/240+Duke+St+W,+Kitchener,+ON+N2H+3X6/@43.4540471,-80.4980085,17z/data=!3m1!4b1!4m6!3m5!1s0x882bf48b1446f47f:0xd77855d158b6c278!8m2!3d43.4540471!4d-80.4954336!16s%2Fg%2F11bw4qwg62?entry=ttu",
			"provinceLicenseNumber": "12403",
			"suiteUnit": null,
			"isCorporate": null,
			"createdAt": "2023-11-06T22:06:19.114Z",
			"updatedAt": "2024-04-14T18:16:55.995Z",
			"publishedAt": "2023-11-06T22:06:22.561Z",
			"documentId": "i9w96dc6hdh4nq495xph9zeg"
		},
		{
			"id": 16,
			"title": "Guelph",
			"slug": "guelph-1",
			"address": "8-292 Stone Road W",
			"city": "Guelph",
			"province": "ontario",
			"postal": "N1G3C4",
			"mapEmbedSrc": "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2893.1986553683323!2d-80.23608492383026!3d43.51904997110885!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x882b8529c9fcc657%3A0x31cce760be30199!2s292%20Stone%20Rd%20W%20%238%2C%20Guelph%2C%20ON%20",
			"mapLocationUrl": "https://www.google.com/maps/place/292+Stone+Rd+W+%238,+Guelph,+ON+N1G+3C4/@43.51905,-80.23351,17z/data=!3m1!4b1!4m6!3m5!1s0x882b8529c9fcc657:0x31cce760be30199!8m2!3d43.51905!4d-80.23351!16s%2Fg%2F11pz7k_z_d?entry=ttu",
			"provinceLicenseNumber": "12403",
			"suiteUnit": null,
			"isCorporate": null,
			"createdAt": "2023-11-06T22:07:35.230Z",
			"updatedAt": "2024-04-14T18:16:10.951Z",
			"publishedAt": "2023-11-06T22:07:38.391Z",
			"documentId": "aerexlwkgimuu4t74qvvmbh0"
		},
		{
			"id": 17,
			"title": "Toronto",
			"slug": "toronto",
			"address": "405 The West Mall Unit 910",
			"city": "Toronto ",
			"province": "ontario",
			"postal": "M9C5J1",
			"mapEmbedSrc": "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2887.1226257386434!2d-79.5669345!3d43.645617!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x882b39f98cf8dce9%3A0x29c731536fa7871e!2s405%20The%20West%20Mall%20%23910%2C%20Etobicoke%2C%20ON%20M9C%20",
			"mapLocationUrl": "https://www.google.com/maps/place/405+The+West+Mall+%23910,+Etobicoke,+ON+M9C+5J5,+Canada/@43.645617,-79.5669345,17z/data=!3m1!4b1!4m6!3m5!1s0x882b39f98cf8dce9:0x29c731536fa7871e!8m2!3d43.645617!4d-79.5669345!16s%2Fg%2F11y1g12zz1?entry=ttu",
			"provinceLicenseNumber": "12403",
			"suiteUnit": null,
			"isCorporate": null,
			"createdAt": "2023-11-06T22:10:28.364Z",
			"updatedAt": "2024-04-14T18:19:22.840Z",
			"publishedAt": "2023-11-06T22:10:33.646Z",
			"documentId": "rwzumg15zn2cd2p13s8epxqo"
		},
		{
			"id": 18,
			"title": "Nova Scotia",
			"slug": "nova-scotia",
			"address": "600-1741 Lower Water Street",
			"city": "Halifax",
			"province": "novaScotia",
			"postal": "B3J0J2",
			"mapEmbedSrc": "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2838.**********!2d-63.57439232241518!3d44.64900847107238!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x4b5a23cc48c154d3%3A0x1a096a8645074165!2sParking%20lot%2C%201741%20Lower%20Water%20St%20Suite",
			"mapLocationUrl": "https://maps.app.goo.gl/EPwgmF2RfhbnJVfo8",
			"provinceLicenseNumber": "3000688",
			"suiteUnit": "600",
			"isCorporate": null,
			"createdAt": "2024-05-30T20:47:22.201Z",
			"updatedAt": "2024-10-31T20:02:53.297Z",
			"publishedAt": "2024-05-30T20:49:23.149Z",
			"documentId": "rl2bt7wvh73kh0719uso5qoa"
		},
		{
			"id": 19,
			"title": "Vernon",
			"slug": "vernon",
			"address": " 5603 27 Street",
			"city": "Vernon",
			"province": "britishColumbia",
			"postal": "V1T8Z5",
			"mapEmbedSrc": "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2748.433150745294!2d-119.26799352345235!3d50.28686287156136!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x537dd8889a24e945%3A0xb248cb5e9fc98617!2s5603%2027%20St%2C%20Vernon%2C%20BC%20V1T%208Z5!5e",
			"mapLocationUrl": "https://maps.app.goo.gl/ea9NdusnViKpmXiB6",
			"provinceLicenseNumber": "029978",
			"suiteUnit": null,
			"isCorporate": null,
			"createdAt": "2024-07-12T18:12:52.677Z",
			"updatedAt": "2024-07-26T17:25:52.308Z",
			"publishedAt": "2024-07-26T17:25:52.021Z",
			"documentId": "m07uwr7nhwfn8yjms1h162l7"
		},
		{
			"id": 20,
			"title": "Edmonton",
			"slug": "edmonton",
			"address": "242, 11150 Jasper Avenue",
			"city": "Edmonton",
			"province": "alberta",
			"postal": "T5K 0C7",
			"mapEmbedSrc": "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2370.931614217235!2d-113.51609598705967!3d53.541135260014954!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x53a02230dd257bd5%3A0xd0268a8f7de4f171!2s11150%20Jasper%20Ave%20%23242%2C%20Edmonton%2C%2",
			"mapLocationUrl": "https://maps.app.goo.gl/jYcBxceE791LkWwA9",
			"provinceLicenseNumber": null,
			"suiteUnit": null,
			"isCorporate": null,
			"createdAt": "2024-07-26T17:20:20.115Z",
			"updatedAt": "2024-07-26T17:20:25.957Z",
			"publishedAt": "2024-07-26T17:20:25.359Z",
			"documentId": "zkvvbhza8mrwghonm2mroj26"
		},
		{
			"id": 21,
			"title": "Olds",
			"slug": null,
			"address": null,
			"city": "Olds",
			"province": "alberta",
			"postal": null,
			"mapEmbedSrc": "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d42573.17280928858!2d-114.14926707110831!3d51.78770310064608!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x53741058940a7299%3A0xa309df7b8a2cd122!2sOlds%2C%20AB!5e1!3m2!1sen!2sca!4v1724338138129!5m",
			"mapLocationUrl": "https://maps.app.goo.gl/3FJpyzSu6kuEVVwx7",
			"provinceLicenseNumber": null,
			"suiteUnit": null,
			"isCorporate": null,
			"createdAt": "2024-08-22T14:49:56.536Z",
			"updatedAt": "2024-08-22T14:50:08.648Z",
			"publishedAt": "2024-08-22T14:50:08.326Z",
			"documentId": "wvyl2rrbzwgbui0o2pw0nkhx"
		},
		{
			"id": 22,
			"title": "Airdrie",
			"slug": "airdrie",
			"address": "133 – 1 Street NW",
			"city": "Airdrie",
			"province": "alberta",
			"postal": "T4B 0R3",
			"mapEmbedSrc": "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d4990.01995006301!2d-114.01541619999999!3d51.29254050000001!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x53715f6ed0dd3e25%3A0xfe0344c2ee0c7b93!2s133%201%20St%20NW%2C%20Airdrie%2C%20AB%20T4B%200R3",
			"mapLocationUrl": "<iframe src=\"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d4990.01995006301!2d-114.01541619999999!3d51.29254050000001!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x53715f6ed0dd3e25%3A0xfe0344c2ee0c7b93!2s133%201%20St%20NW%2C%20Airdrie%2C%20A",
			"provinceLicenseNumber": null,
			"suiteUnit": null,
			"isCorporate": null,
			"createdAt": "2025-01-15T18:14:07.764Z",
			"updatedAt": "2025-01-15T18:14:51.540Z",
			"publishedAt": "2025-01-15T18:14:51.226Z",
			"documentId": "o3psxmtcxiow47iv2d3f46wz"
		},
		{
			"id": 10,
			"title": "Windsor",
			"slug": "windsor",
			"address": "13158 Tecumseh Rd E",
			"city": "Windsor",
			"province": "ontario",
			"postal": "N8N 3T6",
			"mapEmbedSrc": "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d31949745.478704657!2d-84.45774468216352!3d12.172544902673867!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x883ad5230129ff97%3A0xc255156e2c7479fd!2s13158%20Tecumseh%20Rd%20E%2C%20Windsor%2C%20ON%2",
			"mapLocationUrl": "https://goo.gl/maps/rHzwHpdSrQtKQxCJ6",
			"provinceLicenseNumber": "12403",
			"suiteUnit": null,
			"isCorporate": null,
			"createdAt": "2023-04-20T10:59:32.028Z",
			"updatedAt": "2025-08-11T18:58:49.814Z",
			"publishedAt": "2025-08-11T18:58:49.814Z",
			"documentId": "mz93kpm5ipk9iedg2j1rbv9d"
		}
	],
	"meta": {
		"pagination": {
			"page": 1,
			"pageSize": 25,
			"pageCount": 1,
			"total": 22
		}
	}
}