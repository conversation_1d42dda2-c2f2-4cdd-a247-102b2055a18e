import { OnboardingForms } from '@/shared/types/forms';

export interface FormNavigationConfig {
  slug: string;
  title: string;
  order: number;
  isRequired: boolean;
  isUnlicensedOnly?: boolean;
  formKey: keyof OnboardingForms;
}

// Base form configurations - these define all possible forms
export const BASE_FORM_CONFIGS: FormNavigationConfig[] = [
  {
    slug: 'broker-information',
    title: 'Broker Information',
    order: 1,
    isRequired: true,
    formKey: 'brokerInfo',
  },
  {
    slug: 'unlicensed-information',
    title: 'Unlicensed Information',
    order: 2,
    isRequired: true,
    isUnlicensedOnly: true,
    formKey: 'unlicensedInfo',
  },
  {
    slug: 'photos',
    title: 'Photos',
    order: 3,
    isRequired: true,
    formKey: 'photos',
  },
  {
    slug: 'business-card',
    title: 'Business Card',
    order: 4,
    isRequired: true,
    formKey: 'businessCardInfo',
  },
  {
    slug: 'website-information',
    title: 'Website Information',
    order: 5,
    isRequired: true,
    formKey: 'websiteInfo',
  },
  {
    slug: 'mpc-application',
    title: 'MPC Application',
    order: 6,
    isRequired: true,
    formKey: 'mpcApplication',
  },
  {
    slug: 'letter-of-direction',
    title: 'Letter of Direction',
    order: 7,
    isRequired: true,
    formKey: 'letterOfDirection',
  },
  {
    slug: 'payment-authorization',
    title: 'Payment Authorization',
    order: 8,
    isRequired: true,
    formKey: 'paymentAuthorization',
  },
  {
    slug: 'contract-and-schedule',
    title: 'Contract and Schedule',
    order: 9,
    isRequired: true,
    formKey: 'contractAndSchedule',
  },
  {
    slug: 'policies',
    title: 'Policies',
    order: 10,
    isRequired: true,
    isUnlicensedOnly: true,
    formKey: 'policiesAndProcedure',
  },
  {
    slug: 'unlicensed-policies',
    title: 'Unlicensed Policies',
    order: 11,
    isRequired: true,
    isUnlicensedOnly: true,
    formKey: 'unlicensedPolicies',
  },
];

export function getFormConfig(slug: string): FormNavigationConfig | undefined {
  return BASE_FORM_CONFIGS.find(config => config.slug === slug);
}

/**
 * Generate dynamic form configuration based on API response and user license status
 * This is the main function that should be used instead of hardcoded FORM_CONFIGS
 */
export function generateFormConfigs(
  forms: OnboardingForms, 
  userLicensed: boolean
): FormNavigationConfig[] {
  const availableForms: FormNavigationConfig[] = [];
  
  // Check which forms are actually returned from the API
  const hasBrokerInfo = forms.brokerInfo && forms.brokerInfo !== null;
  const hasUnlicensedInfo = forms.unlicensedInfo && forms.unlicensedInfo !== null;
  
  // Rule 1: If both forms are returned, check user.licensed field
  if (hasBrokerInfo && hasUnlicensedInfo) {
    if (userLicensed) {
      // User is licensed, include brokerInfo and exclude unlicensedInfo
      availableForms.push(...BASE_FORM_CONFIGS.filter(config => 
        config.formKey !== 'unlicensedInfo' && 
        forms[config.formKey] !== null && 
        forms[config.formKey] !== undefined
      ));
    } else {
      // User is unlicensed, include unlicensedInfo and exclude brokerInfo
      availableForms.push(...BASE_FORM_CONFIGS.filter(config => 
        config.formKey !== 'brokerInfo' && 
        forms[config.formKey] !== null && 
        forms[config.formKey] !== undefined
      ));
    }
  } else {
    // Rule 2: If only one form is returned, include it regardless of license status
    availableForms.push(...BASE_FORM_CONFIGS.filter(config => 
      forms[config.formKey] !== null && 
      forms[config.formKey] !== undefined
    ));
  }
  
  // Sort by order
  return availableForms.sort((a, b) => a.order - b.order);
}

export function getNextForm(
  currentSlug: string, 
  forms: OnboardingForms, 
  userLicensed: boolean
): string | null {
  const availableForms = generateFormConfigs(forms, userLicensed);
  const currentIndex = availableForms.findIndex(config => config.slug === currentSlug);
  
  if (currentIndex === -1 || currentIndex === availableForms.length - 1) {
    return null;
  }
  
  return availableForms[currentIndex + 1].slug;
}

export function getPreviousForm(
  currentSlug: string, 
  forms: OnboardingForms, 
  userLicensed: boolean
): string | null {
  const availableForms = generateFormConfigs(forms, userLicensed);
  const currentIndex = availableForms.findIndex(config => config.slug === currentSlug);
  
  if (currentIndex <= 0) {
    return null;
  }
  
  return availableForms[currentIndex - 1].slug;
}

export function getAvailableForms(forms: OnboardingForms, userLicensed: boolean): FormNavigationConfig[] {
  return generateFormConfigs(forms, userLicensed);
}

export function calculateCompletionPercentage(forms: OnboardingForms, userLicensed: boolean): number {
  const availableForms = getAvailableForms(forms, userLicensed);
  const totalForms = availableForms.length;
  
  if (totalForms === 0) return 0;
  
  let completedForms = 0;
  
  availableForms.forEach(config => {
    const formData = forms[config.formKey] as any;
    
    if (formData && formData.isFormComplete) {
      completedForms++;
    }
  });
  
  return Math.round((completedForms / totalForms) * 100);
}

function getFormKey(slug: string): keyof OnboardingForms {
  const keyMap: Record<string, keyof OnboardingForms> = {
    'broker-information': 'brokerInfo',
    'unlicensed-information': 'unlicensedInfo',
    'photos': 'photos',
    'business-card': 'businessCardInfo',
    'website-information': 'websiteInfo',
    'mpc-application': 'mpcApplication',
    'letter-of-direction': 'letterOfDirection',
    'payment-authorization': 'paymentAuthorization',
    'contract-and-schedule': 'contractAndSchedule',
    'policies': 'unlicensedPolicies',
  };

  return keyMap[slug] || 'brokerInfo';
}

// React hook for form navigation
export function useFormNavigation() {
  if (typeof window === 'undefined') {
    // Server-side fallback
    return {
      getCurrentFormIndex: () => 0,
      getTotalForms: () => 0,
      goToPreviousForm: () => {},
      goToNextForm: () => {},
      getCurrentForm: () => null,
      formMenuItems: [],
    };
  }

  // This will be imported dynamically on client side
  const { useRouter, usePathname } = require("next/navigation");
  const { useFormsContext } = require("@/shared/contexts/forms-context");
  const { useAuthContext } = require("@/shared/contexts/auth-context");

  const router = useRouter();
  const pathname = usePathname();
  const { forms, menuOrder } = useFormsContext();
  const { userAuth } = useAuthContext();

  // Generate dynamic form configuration based on API data and user license status
  const userLicensed = userAuth?.userInfo?.licensed === true;
  const formMenuItems = generateFormConfigs(forms, userLicensed);

  // Get current form index based on pathname
  const getCurrentFormIndex = () => {
    if (!formMenuItems || !Array.isArray(formMenuItems)) {
      return 0;
    }
    const currentSlug = pathname?.split('/').pop();
    const index = formMenuItems.findIndex((item: any) => item.slug === currentSlug);
    return index >= 0 ? index : 0;
  };

  // Get total number of forms
  const getTotalForms = () => formMenuItems?.length || 0;

  // Navigate to previous form
  const goToPreviousForm = () => {
    if (!formMenuItems || !Array.isArray(formMenuItems)) return;
    const currentIndex = getCurrentFormIndex();
    if (currentIndex > 0) {
      const previousForm = formMenuItems[currentIndex - 1];
      router.push(`/webforms/${previousForm.slug}`);
    }
  };

  // Navigate to next form
  const goToNextForm = () => {
    if (!formMenuItems || !Array.isArray(formMenuItems)) return;
    const currentIndex = getCurrentFormIndex();
    if (currentIndex < formMenuItems.length - 1) {
      const nextForm = formMenuItems[currentIndex + 1];
      router.push(`/webforms/${nextForm.slug}`);
    }
  };

  // Get current form info
  const getCurrentForm = () => {
    if (!formMenuItems || !Array.isArray(formMenuItems)) return null;
    const currentIndex = getCurrentFormIndex();
    return currentIndex >= 0 && currentIndex < formMenuItems.length ? formMenuItems[currentIndex] : null;
  };

  return {
    getCurrentFormIndex,
    getTotalForms,
    goToPreviousForm,
    goToNextForm,
    getCurrentForm,
    formMenuItems,
  };
}
