// helpers/signatureHelper.ts

import SignatureCanvas from "react-signature-canvas";
import * as htmlToImage from "html-to-image";

/**
 * Captures signature from canvas and converts it to a blob
 * Works across different browsers including Safari
 *
 * @param signaturePadRef - Reference to the SignaturePad component
 * @param containerRef - Reference to the container div
 * @returns Promise that resolves to the signature blob
 */
export const captureSignature = async (
  signaturePadRef: React.RefObject<SignatureCanvas>,
  containerRef: React.RefObject<HTMLDivElement>
): Promise<Blob> => {
  if (!signaturePadRef.current || signaturePadRef.current.isEmpty()) {
    throw new Error('Signature is empty');
  }

  try {
    let signatureBlob: Blob;

    // Safari-specific handling
    if (/^((?!chrome|android).)*safari/i.test(navigator.userAgent)) {
      // Get the canvas element and its data directly
      const canvas = signaturePadRef.current.getCanvas();
      const dataUrl = canvas.toDataURL('image/png');

      // Convert base64 to blob
      const base64Response = await fetch(dataUrl);
      signatureBlob = await base64Response.blob();
    } else {
      // For other browsers, use html-to-image
      if (!containerRef.current) {
        throw new Error('Container reference not found');
      }
      const blob = await htmlToImage.toBlob(containerRef.current);
      if (!blob) {
        throw new Error('Failed to generate signature blob');
      }
      signatureBlob = blob;
    }

    // Validate blob
    if (!signatureBlob || signatureBlob.size < 1000) {
      throw new Error('Generated signature appears to be empty or invalid');
    }

    return signatureBlob;
  } catch (error) {
    throw new Error(`Failed to capture signature: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Creates a FormData object with the signature blob and metadata
 *
 * @param signatureBlob - The signature blob
 * @param user - User object containing firstname and lastname
 * @param fieldName - Name of the field in the backend (e.g., 'signature')
 * @returns FormData object ready for upload
 */
export const createSignatureFormData = (
  signatureBlob: Blob,
  user: { firstname?: string; lastName?: string; id: string },
  fieldName = 'signature'
): FormData => {
  const formData = new FormData();
  const firstName = user.firstname || 'unknown';
  const lastName = user.lastName || 'unknown';
  
  formData.append('files', signatureBlob, `applicantSignature-${firstName}-${lastName}.png`);
  formData.append('refId', user.id);
  formData.append('source', 'users-permissions');
  formData.append('ref', 'user');
  formData.append('field', fieldName);
  return formData;
};

/**
 * Checks if a signature canvas has been signed by the user
 * This is more reliable than just checking isEmpty() as it validates actual content
 *
 * @param signaturePadRef - Reference to the SignaturePad component
 * @returns boolean indicating if signature has meaningful content
 */
export const hasValidSignature = (signaturePadRef: React.RefObject<SignatureCanvas>): boolean => {
  if (!signaturePadRef.current || signaturePadRef.current.isEmpty()) {
    return false;
  }

  try {
    // Get the canvas and its image data
    const canvas = signaturePadRef.current.getCanvas();
    const ctx = canvas.getContext('2d');
    
    if (!ctx) {
      return false;
    }

    // Get image data from the canvas
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;
    
    // Count non-white pixels (assuming white background)
    let nonWhitePixels = 0;
    for (let i = 0; i < data.length; i += 4) {
      // Check if pixel is not white (RGB values not all 255)
      if (data[i] !== 255 || data[i + 1] !== 255 || data[i + 2] !== 255) {
        nonWhitePixels++;
      }
    }
    
    // Consider signature valid if there are enough non-white pixels
    // This threshold can be adjusted based on requirements
    return nonWhitePixels > 100;
  } catch (error) {
    console.error('Error validating signature:', error);
    return false;
  }
};

/**
 * Clears the signature canvas and resets its state
 *
 * @param signaturePadRef - Reference to the SignaturePad component
 */
export const clearSignature = (signaturePadRef: React.RefObject<SignatureCanvas>): void => {
  if (signaturePadRef.current) {
    signaturePadRef.current.clear();
  }
};

/**
 * Loads a signature from a data URL into the canvas
 *
 * @param signaturePadRef - Reference to the SignaturePad component
 * @param dataUrl - The data URL of the signature to load
 */
export const loadSignature = (
  signaturePadRef: React.RefObject<SignatureCanvas>,
  dataUrl: string
): void => {
  if (signaturePadRef.current && dataUrl) {
    signaturePadRef.current.fromDataURL(dataUrl);
  }
};

/**
 * Gets the signature as a data URL
 *
 * @param signaturePadRef - Reference to the SignaturePad component
 * @returns The signature as a data URL or null if empty
 */
export const getSignatureDataURL = (signaturePadRef: React.RefObject<SignatureCanvas>): string | null => {
  if (!signaturePadRef.current || signaturePadRef.current.isEmpty()) {
    return null;
  }
  
  return signaturePadRef.current.toDataURL();
};

/**
 * Saves signature to the backend and updates user data
 * Replicates the legacy signature saving logic
 *
 * @param signatureDataUrl - The signature as a data URL
 * @param user - User object
 * @param api - API client instance
 * @param fieldName - Field name for the signature (default: 'signature')
 * @returns Promise that resolves to the uploaded signature data
 */
export const saveSignatureToBackend = async (
  signatureDataUrl: string,
  user: { firstname?: string; lastname?: string; id: string },
  api: any,
  fieldName = 'signature'
): Promise<any> => {
  try {
    // Convert data URL to blob
    const response = await fetch(signatureDataUrl);
    const signatureBlob = await response.blob();
    
    // Create form data for upload
    const formData = createSignatureFormData(signatureBlob, user, fieldName);
    
    // Upload signature to backend
    const uploadResponse = await api.post('/upload', formData);
    
    if (!uploadResponse.success || !uploadResponse.data) {
      throw new Error('Failed to upload signature');
    }
    
    const signatureData = uploadResponse.data[0];
    
    // Update user with signature data
    const userUpdateResponse = await api.put(`/users/${user.id}`, {
      [fieldName]: signatureData
    });
    
    if (!userUpdateResponse.success) {
      throw new Error('Failed to update user signature');
    }
    
    return signatureData;
  } catch (error) {
    console.error('Error saving signature:', error);
    throw new Error(`Failed to save signature: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Checks if a signature exists and is saved
 * Replicates the legacy checkSignature function
 *
 * @param signature - Signature object from form data
 * @returns Object with isSaved status and signature data
 */
export const checkSignature = (signature: any): { isSaved: boolean; data?: any } => {
  if (signature && signature !== null && signature.url && signature.url.length > 0) {
    return { isSaved: true, data: signature };
  }
  return { isSaved: false };
};
