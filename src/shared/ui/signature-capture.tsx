"use client";

import * as React from "react";
import SignatureCanvas from "react-signature-canvas";
import { Button } from "@/shared/ui/button";
import { Label } from "@/shared/ui/label";
import { cn } from "@/shared/lib/utils";
import {
  hasValidSignature,
  clearSignature,
  loadSignature,
  getSignatureDataURL,
  captureSignature,
  createSignatureFormData
} from "@/shared/lib/signature-helper";

interface SignatureCaptureProps {
  onSignatureChange: (signature: string | null) => void;
  onSignatureSave?: (signature: string) => Promise<void>;
  value?: string;
  label?: string;
  required?: boolean;
  className?: string;
  disabled?: boolean;
  showSaveButton?: boolean;
}

export interface SignatureCaptureRef {
  captureSignature: () => Promise<Blob>;
  createFormData: (signatureBlob: Blob, user: any, fieldName?: string) => FormData;
  hasValidSignature: () => boolean;
  clear: () => void;
  saveSignature: () => Promise<void>;
  isSignatureSaved: () => boolean;
}

export const SignatureCapture = React.forwardRef<SignatureCaptureRef, SignatureCaptureProps>(({
  onSignatureChange,
  onSignatureSave,
  value,
  label = "Signature",
  required = false,
  className,
  disabled = false,
  showSaveButton = true,
}, ref) => {
  const signaturePadRef = React.useRef<SignatureCanvas>(null);
  const containerRef = React.useRef<HTMLDivElement>(null);
  const [hasSignature, setHasSignature] = React.useState(false);
  const [isSignatureSaved, setIsSignatureSaved] = React.useState(false);
  const [isSaving, setIsSaving] = React.useState(false);

  const handleClear = () => {
    if (signaturePadRef.current) {
      clearSignature(signaturePadRef as React.RefObject<SignatureCanvas>);
    }
    setHasSignature(false);
    setIsSignatureSaved(false);
    onSignatureChange(null);
  };

  const handleEnd = () => {
    if (signaturePadRef.current) {
      const isValid = hasValidSignature(signaturePadRef as React.RefObject<SignatureCanvas>);
      setHasSignature(isValid);
      // Don't auto-save, just update the state
      if (isValid) {
        const dataURL = getSignatureDataURL(signaturePadRef as React.RefObject<SignatureCanvas>);
        onSignatureChange(dataURL);
      } else {
        onSignatureChange(null);
      }
    }
  };

  const handleSaveSignature = async () => {
    if (!signaturePadRef.current || !hasValidSignature(signaturePadRef as React.RefObject<SignatureCanvas>)) {
      return;
    }

    setIsSaving(true);
    try {
      const dataURL = getSignatureDataURL(signaturePadRef as React.RefObject<SignatureCanvas>);
      
      if (onSignatureSave) {
        await onSignatureSave(dataURL);
        setIsSignatureSaved(true);
      } else {
        // Fallback to just updating the signature
        onSignatureChange(dataURL);
        setIsSignatureSaved(true);
      }
    } catch (error) {
      console.error('Error saving signature:', error);
    } finally {
      setIsSaving(false);
    }
  };

  // Expose helper functions for external use
  const captureSignatureBlob = async (): Promise<Blob> => {
    if (signaturePadRef.current && containerRef.current) {
      return captureSignature(signaturePadRef as React.RefObject<SignatureCanvas>, containerRef as React.RefObject<HTMLDivElement>);
    }
    throw new Error('Signature pad not available');
  };

  const createFormData = (signatureBlob: Blob, user: any, fieldName = 'signature'): FormData => {
    return createSignatureFormData(signatureBlob, user, fieldName);
  };

  // Expose methods via ref
  React.useImperativeHandle(ref, () => ({
    captureSignature: captureSignatureBlob,
    createFormData,
    hasValidSignature: () => signaturePadRef.current ? hasValidSignature(signaturePadRef as React.RefObject<SignatureCanvas>) : false,
    clear: handleClear,
    saveSignature: handleSaveSignature,
    isSignatureSaved: () => isSignatureSaved,
  }));

  // Load existing signature if provided
  React.useEffect(() => {
    if (value && signaturePadRef.current) {
      loadSignature(signaturePadRef as React.RefObject<SignatureCanvas>, value);
      setHasSignature(hasValidSignature(signaturePadRef as React.RefObject<SignatureCanvas>));
    }
  }, [value]);

  return (
    <div className={cn("space-y-2", className)}>
      <Label htmlFor="signature">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </Label>

      {/* Signature container matching legacy structure */}
      <div className={cn(
        disabled && "opacity-50 pointer-events-none"
      )}>
        <div
          ref={containerRef}
          style={{
            display: 'table',
            border: '1px solid #ccc',
            width: '500px',
            margin: '0',
            padding: '0',
            background: 'white'
          }}
        >
          <SignatureCanvas
            ref={signaturePadRef}
            canvasProps={{
              width: 500,
              height: 200,
              style: {
                width: '500px',
                height: '200px',
                display: 'block',
                border: 'none',
                background: 'white'
              }
            }}
            onEnd={handleEnd}
            penColor="black"
            backgroundColor="rgb(255, 255, 255)"
          />
        </div>

        <div className="flex justify-between items-center mt-2">
          <p className="text-sm text-muted-foreground">
            {isSignatureSaved ? "Signature saved" : hasSignature ? "Signature ready to save" : "Please sign above"}
          </p>

          <div className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleClear}
              disabled={disabled}
            >
              Clear
            </Button>
            
            {showSaveButton && (
              <Button
                type="button"
                variant="default"
                size="sm"
                onClick={handleSaveSignature}
                disabled={disabled || !hasSignature || isSaving}
              >
                {isSaving ? "Saving..." : "Save Signature"}
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
});

SignatureCapture.displayName = "SignatureCapture";

// Export helper functions for use in forms
export { SignatureCapture as default };
export type { SignatureCaptureProps };
