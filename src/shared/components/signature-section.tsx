"use client";

import * as React from "react";
import { SignatureCapture } from "@/shared/ui/signature-capture";
import { saveSignatureToBackend, checkSignature } from "@/shared/lib/signature-helper";
import { api } from "@/shared/lib/api";
import { useAuthContext } from "@/shared/contexts/auth-context";
import { toast } from "@/shared/lib/toast";

interface SignatureSectionProps {
  value?: string;
  onSignatureChange: (signature: string | null) => void;
  setValue: (field: any, value: any) => void;
  fieldName?: string;
  label?: string;
  required?: boolean;
  className?: string;
}

export function SignatureSection({
  value,
  onSignatureChange,
  setValue,
  fieldName = 'signature',
  label = "Signature",
  required = true,
  className
}: SignatureSectionProps) {
  const { userAuth } = useAuthContext();
  const [signature, setSignature] = React.useState<{ isSaved: boolean; data?: any }>({ isSaved: false, data: null });
  const [isSavingSignature, setIsSavingSignature] = React.useState(false);

  // Handle signature saving
  const handleSignatureSave = async (signatureDataUrl: string) => {
    setIsSavingSignature(true);
    try {
      const user = {
        firstname: userAuth?.userInfo?.firstname || '',
        lastname: userAuth?.userInfo?.lastname || '',
        id: userAuth?.userInfo?.id || ''
      };

      const signatureData = await saveSignatureToBackend(signatureDataUrl, user, api, fieldName);
      setSignature({ isSaved: true, data: signatureData });
      setValue(fieldName, signatureDataUrl);
      onSignatureChange(signatureDataUrl);
      toast.success('Signature saved successfully');
    } catch (error) {
      console.error('Error saving signature:', error);
      toast.error('Failed to save signature');
    } finally {
      setIsSavingSignature(false);
    }
  };

  // Check if signature exists on component mount
  React.useEffect(() => {
    if (value) {
      const signatureCheck = checkSignature({ url: value });
      setSignature(signatureCheck);
    }
  }, [value]);

  return (
    <div className={className}>
      <SignatureCapture
        value={value}
        onSignatureChange={onSignatureChange}
        onSignatureSave={handleSignatureSave}
        required={required}
        showSaveButton={true}
        label={label}
      />

      {/* Signature status message */}
      <div className="mt-2">
        <p className="text-sm text-muted-foreground">
          {signature.isSaved ? '' : 'Please save your signature first.'}
        </p>
      </div>
    </div>
  );
}

// Export helper to check if signature is saved
export function useSignatureStatus(value?: string) {
  const [signature, setSignature] = React.useState<{ isSaved: boolean; data?: any }>({ isSaved: false, data: null });

  React.useEffect(() => {
    if (value) {
      const signatureCheck = checkSignature({ url: value });
      setSignature(signatureCheck);
    }
  }, [value]);

  return signature;
}
