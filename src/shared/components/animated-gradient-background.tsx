import React from 'react';

const AnimatedGradientBackground = ({ className = '' }) => {
  return (
    <div className={`animated-gradient-bg ${className}`}>
      <style jsx>{`
        .animated-gradient-bg {
          position: fixed;
          top: 0;
          left: 0;
          width: 100vw;
          height: 100vh;
          z-index: -1;
          background: linear-gradient(
            45deg,
            #478482,
            #C6A143,
            #3CABA7,
            #3C79B2,
            #415A71,
            #2A7A94,
            #6D9DB8,
            #478482,
            #C6A143,
            #3CABA7
          );
          background-size: 600% 600%;
          animation: gradientShift 12s ease infinite;
        }

        @keyframes gradientShift {
          0% {
            background-position: 0% 50%;
          }
          25% {
            background-position: 100% 50%;
          }
          50% {
            background-position: 100% 100%;
          }
          75% {
            background-position: 50% 100%;
          }
          100% {
            background-position: 0% 50%;
          }
        }

        /* Additional floating color orbs for more dynamic effect */
        .animated-gradient-bg::before,
        .animated-gradient-bg::after {
          content: '';
          position: absolute;
          border-radius: 50%;
          opacity: 0.8;
        }

        .animated-gradient-bg::before {
          top: 15%;
          left: 15%;
          width: 500px;
          height: 500px;
          background: radial-gradient(circle, #254F5C, transparent);
          filter: blur(120px);
          animation: float 20s ease-in-out infinite;
        }

        .animated-gradient-bg::after {
          bottom: 15%;
          right: 15%;
          width: 600px;
          height: 600px;
          background: radial-gradient(circle, #005471, transparent);
          filter: blur(120px);
          animation: float 25s ease-in-out infinite reverse;
        }

        @keyframes float {
          0%, 100% {
            transform: translateY(0px) translateX(0px) scale(1);
          }
          25% {
            transform: translateY(-30px) translateX(20px) scale(1.1);
          }
          50% {
            transform: translateY(-60px) translateX(-20px) scale(0.9);
          }
          75% {
            transform: translateY(-30px) translateX(-40px) scale(1.05);
          }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
          .animated-gradient-bg::before {
            width: 350px;
            height: 350px;
          }
          
          .animated-gradient-bg::after {
            width: 400px;
            height: 400px;
          }
        }
      `}</style>
    </div>
  );
};

export default AnimatedGradientBackground;