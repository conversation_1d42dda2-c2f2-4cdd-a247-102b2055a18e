"use client";

import * as React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { unlicensedInfoSchema, UnlicensedInfoFormData } from "../lib/validation";
import { useFormsContext } from "@/shared/contexts/forms-context";
import { useAuthContext } from "@/shared/contexts/auth-context";
import { toast } from "@/shared/lib/toast";

export function useUnlicensedInfoForm() {
  const { userAuth } = useAuthContext();
  const { forms, updateForm, saveForm } = useFormsContext();
  const [isLoading, setIsLoading] = React.useState(false);
  const [charCount, setCharCount] = React.useState({ bio: 0, note: 0 });
  const [sameAddressWarning, setSameAddressWarning] = React.useState<{
    showMessage: boolean;
    sameAddress: boolean | null;
  }>({
    showMessage: false,
    sameAddress: null
  });
  const [branches, setBranches] = React.useState([]);

  const form = useForm<UnlicensedInfoFormData>({
    resolver: zodResolver(unlicensedInfoSchema) as any,
    defaultValues: {
      firstname: (forms.unlicensedInfo as any)?.firstname || userAuth.userInfo?.firstname || "",
      middlename: (forms.unlicensedInfo as any)?.middlename || userAuth.userInfo?.middlename || "",
      lastname: (forms.unlicensedInfo as any)?.lastname || userAuth.userInfo?.lastname || "",
      workEmail: (forms.unlicensedInfo as any)?.workEmail || userAuth.userInfo?.email || "",
      cellPhone: (forms.unlicensedInfo as any)?.cellPhone || userAuth.userInfo?.phone || "",
      emergencyContact: (forms.unlicensedInfo as any)?.emergencyContact || "",
      emergencyPhone: (forms.unlicensedInfo as any)?.emergencyPhone || "",
      assistantTo: (forms.unlicensedInfo as any)?.assistantTo || "",
      completingCompliance: (forms.unlicensedInfo as any)?.completingCompliance || false,
      address: (forms.unlicensedInfo as any)?.address || "",
      suiteUnit: (forms.unlicensedInfo as any)?.suiteUnit || "",
      city: (forms.unlicensedInfo as any)?.city || "",
      province: (forms.unlicensedInfo as any)?.province || "",
      postalCode: (forms.unlicensedInfo as any)?.postalCode || "",
      personalAddress: (forms.unlicensedInfo as any)?.personalAddress || "",
      personalSuiteUnit: (forms.unlicensedInfo as any)?.personalSuiteUnit || "",
      personalCity: (forms.unlicensedInfo as any)?.personalCity || "",
      personalProvince: (forms.unlicensedInfo as any)?.personalProvince || "",
      personalPostalCode: (forms.unlicensedInfo as any)?.personalPostalCode || "",
      signature: (forms.unlicensedInfo as any)?.signature || "",
    },
  });

  const { watch, setValue, handleSubmit, formState: { errors } } = form;
  const watchedValues = watch();

  // Load existing form data
  React.useEffect(() => {
    if (forms.unlicensedInfo) {
      const formData = forms.unlicensedInfo;

      // Define the form fields that should be loaded
      const formFields: (keyof UnlicensedInfoFormData)[] = [
        'firstname', 'middlename', 'lastname', 'workEmail', 'cellPhone',
        'emergencyContact', 'emergencyPhone', 'assistantTo', 'completingCompliance',
        'address', 'suiteUnit', 'city', 'province', 'postalCode',
        'personalAddress', 'personalSuiteUnit', 'personalCity', 'personalProvince',
        'personalPostalCode', 'signature'
      ];

      formFields.forEach((field) => {
        if (formData[field as keyof typeof formData] !== undefined && formData[field as keyof typeof formData] !== null) {
          setValue(field, formData[field as keyof typeof formData] as any);
        }
      });
    }
  }, [forms.unlicensedInfo, setValue]);

  // Load branches data
  React.useEffect(() => {
    const fetchBranches = async () => {
      try {
        const response = await fetch('/api/branches');
        if (response.ok) {
          const data = await response.json();
          setBranches(data);
        }
      } catch (error) {
        console.error('Error fetching branches:', error);
      }
    };
    fetchBranches();
  }, []);

  // Handle form submission
  const onSubmit = async (data: UnlicensedInfoFormData) => {
    setIsLoading(true);
    try {
      // Update form data in context
      updateForm('unlicensedInfo', {
        ...data,
        isFormComplete: true,
        firstSaveComplete: true,
        lastUpdated: new Date().toISOString(),
      });

      // Save to backend
      await saveForm('unlicensedInfo');
      
      toast.success("Unlicensed information saved successfully!");
    } catch (error) {
      console.error("Error saving unlicensed information:", error);
      toast.error("Failed to save unlicensed information");
    } finally {
      setIsLoading(false);
    }
  };

  // Handle character counts
  const handleBioChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setCharCount(prev => ({ ...prev, bio: value.length }));
    // setValue('bio', value); // Field not in schema
  };

  const handleNoteChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setCharCount(prev => ({ ...prev, note: value.length }));
    // setValue('note', value); // Field not in schema
  };

  // Handle same address toggle
  const handleSameAddressChange = (checked: boolean) => {
    // setValue('sameAddress', checked); // Field not in schema
    if (checked) {
      setValue('personalAddress', watchedValues.address);
      setValue('personalCity', watchedValues.city);
      setValue('personalProvince', watchedValues.province);
      setValue('personalPostalCode', watchedValues.postalCode);
    }
  };

  // Handle address selection from branches
  const handleBranchSelect = (selectedAddress: any) => {
    setValue('address', selectedAddress.address || '');
    setValue('city', selectedAddress.city || '');
    setValue('province', selectedAddress.province || '');
    setValue('postalCode', selectedAddress.postalCode || '');
  };

  // Check if signature exists
  const hasSignature = () => {
    const signature = watchedValues.signature;
    if (!signature) return false;
    if (typeof signature === 'string') {
      return signature.length > 0;
    }
    if (typeof signature === 'object' && signature.url) {
      return true;
    }
    return false;
  };

  return {
    form,
    watchedValues,
    errors,
    isLoading,
    charCount,
    sameAddressWarning,
    setSameAddressWarning,
    branches,
    hasSignature,
    handleBioChange,
    handleNoteChange,
    handleSameAddressChange,
    handleBranchSelect,
    handleSubmit,
    onSubmit,
  };
}
