"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuthContext } from "@/shared/contexts/auth-context";
import { Button } from "@/shared/ui/button";
import { Card, CardContent } from "@/shared/ui/card";
import { Alert, AlertDescription } from "@/shared/ui/alert";
import { AlertCircle, ArrowRight } from "lucide-react";
import Image from "next/image";

export default function DashboardPage() {
  const { userAuth } = useAuthContext();
  const router = useRouter();
  const [fieldsValidation, setFieldsValidation] = useState<string[]>([]);
  const [lastForm, setLastForm] = useState<string | null>(null);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (userAuth.initialized && !userAuth.isAuth) {
      router.push("/login");
    }
  }, [userAuth.initialized, userAuth.isAuth, router]);

  // Broker info form validation (mimicking legacy logic)
  const brokerInfoFormValidation = () => {
    if (!userAuth.userInfo) return;

    const fields = [
      'firstname',
      'lastname',
      'workEmail',
      'position',
      'address',
      'city',
      'province',
      'postalCode',
      'phone',
      'cellPhone',
      'emergencyContact',
      'emergencyPhone',
      'birthdate',
      'sin',
      'startDate'
    ];

    const filteredFields = () => {
      const filtered: string[] = [];
      const user = userAuth.userInfo;
      
      for (const f of fields) {
        const value = (user as any)[f];
        if (value === null || value === '' || value === undefined) {
          filtered.push(f);
        }
      }
      return filtered;
    };

    if (filteredFields().length > 0) {
      const validatedFields = filteredFields();
      const fieldsMessages = validatedFields.map((f) => {
        switch (f) {
          case 'firstname':
            return 'First Name';
          case 'lastname':
            return 'Last Name';
          case 'workEmail':
            return 'Work Email';
          case 'position':
            return 'Position';
          case 'address':
            return 'Address';
          case 'city':
            return 'City';
          case 'province':
            return 'Province';
          case 'postalCode':
            return 'Postal Code';
          case 'phone':
            return 'Work Phone';
          case 'cellPhone':
            return 'Cell Phone';
          case 'emergencyContact':
            return 'Emergency Contact';
          case 'emergencyPhone':
            return 'Emergency Phone';
          case 'birthdate':
            return 'Birth Date';
          case 'sin':
            return 'SIN';
          case 'startDate':
            return 'Start Date';
          default:
            return '';
        }
      });

      if (fieldsMessages.length > 0) {
        setFieldsValidation(fieldsMessages);
      }
    }
  };

  const welcomeMessage = () => {
    if (userAuth.userInfo && userAuth.userInfo.loginCount && parseInt(String(userAuth.userInfo.loginCount)) >= 0 && parseInt(String(userAuth.userInfo.loginCount)) < 2) {
      return <h3 className="text-2xl font-bold text-center">Indi Onboarding</h3>;
    } else {
      if (userAuth.userInfo && userAuth.userInfo.firstname) {
        return <h3 className="text-2xl font-bold text-center">{userAuth.userInfo.firstname}, welcome back!</h3>;
      } else {
        return <h3 className="text-2xl font-bold text-center">Indi Onboarding - Welcome!</h3>;
      }
    }
  };

  const lastFormOpened = (form: string | null) => {
    if (form && form !== null && form !== undefined) {
      return setLastForm(form);
    } else {
      if (userAuth.userInfo && userAuth.userInfo.licensed === false) {
        return setLastForm('unlicensed-information');
      } else {
        return setLastForm('broker-information');
      }
    }
  };

  useEffect(() => {
    brokerInfoFormValidation();
    // Set default form based on user status
    if (userAuth.userInfo && userAuth.userInfo.licensed === false) {
      lastFormOpened('unlicensed-information');
    } else {
      lastFormOpened('broker-information');
    }
  }, [userAuth.userInfo]);

  // Show loading state while auth is initializing
  if (!userAuth.initialized) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  // Don't render if not authenticated (will redirect)
  if (!userAuth.isAuth) {
    return null;
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Banner Section */}
        <div className="mb-8">
          <div className="relative w-full h-48 md:h-64 rounded-lg overflow-hidden">
            <Image
              src="/images/bg-welcome.jpg"
              alt="dashboard banner"
              fill
              className="object-cover"
              priority
            />
          </div>
        </div>

        {/* Content Section */}
        <div className="max-w-4xl mx-auto">
          <article className="text-center mb-8">
            {welcomeMessage()}
            <p className="text-muted-foreground mt-4 text-lg">
              Welcome to your digital onboarding experience. Please take a few minutes to fill out the information
              required to complete your transition to Indi. This information is necessary in adhering to regulatory
              compliance, lender set up and marketing (both initial and ongoing).
            </p>
            <p className="text-muted-foreground mt-4 text-lg">
              You may fill in the forms in any order, start a form and finish it later and edit at any time…however,
              the quicker you are in completing the forms, the quicker we can be to getting you on your way to doing
              the fun stuff - helping your clients!
            </p>
          </article>          

          {/* Action Button */}
          <div className="flex justify-center">
            <Button
              size="lg"
              className="px-8 py-3 text-lg"
              onClick={() => router.push(`/webforms/${lastForm}`)}
            >
              {userAuth.userInfo && userAuth.userInfo.loginCount && parseInt(String(userAuth.userInfo.loginCount)) >= 0 && parseInt(String(userAuth.userInfo.loginCount)) < 2
                ? 'Start Here!'
                : 'Continue Onboarding!'
              }
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
