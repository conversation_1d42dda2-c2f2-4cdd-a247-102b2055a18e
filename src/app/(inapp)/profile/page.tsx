"use client";

import React from "react";
import { useAuth } from "@/shared/hooks/use-auth";
import { Loading } from "@/shared/ui/loading";
import { Card, CardContent, CardHeader, CardTitle } from "@/shared/ui/card";

export default function ProfilePage() {
  const { user, isLoading: authLoading } = useAuth();

  // Show loading state
  if (authLoading || !user) {
    return <Loading />;
  }

  return (
    <div className="container mx-auto py-6 px-4 max-w-4xl">
      <div className="space-y-6">
        {/* Header */}
        <div className="space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">Profile</h1>
          <p className="text-muted-foreground">
            Manage your account settings and profile information.
          </p>
        </div>

        {/* Placeholder Content */}
        <Card>
          <CardHeader>
            <CardTitle>Profile Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <strong>Name:</strong> {user?.firstname} {user?.lastname}
              </div>
              <div>
                <strong>Email:</strong> {user?.email}
              </div>
              <div>
                <strong>License:</strong> {user?.license ? 'Licensed' : 'Unlicensed'}
              </div>
              <p className="text-muted-foreground mt-4">
                Profile management features are coming soon.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
