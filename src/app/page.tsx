import { redirect } from "next/navigation";
import { checkAuthAction } from "@/shared/actions/auth";

export default async function HomePage() {
  // Check authentication status on the server
  const { isAuthenticated } = await checkAuthAction();
  
  if (isAuthenticated) {
    // User is authenticated, redirect to dashboard
    redirect("/dashboard");
  } else {
    // User is not authenticated, redirect to login
    redirect("/login");
  }
}
