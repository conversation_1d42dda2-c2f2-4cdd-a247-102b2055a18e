<svg width="131" height="130" viewBox="0 0 131 130" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d)">
<path d="M56.5106 91.2348C56.5106 98.7465 56.5106 97.8545 43.8882 97.8545C31.2659 97.8545 31.2659 98.7465 31.2659 91.2348C31.2659 83.723 36.8916 75.601 43.8882 75.601C50.8849 75.601 56.5106 83.723 56.5106 91.2348Z" fill="url(#paint0_radial)"/>
<path d="M35.1897 91.892C36.2297 99.3568 36.0879 97.2441 34.8588 97.7606C31.5023 99.1221 29.7531 98.3709 30.3204 91.1408C30.8877 83.6291 36.2297 77.7136 40.1063 76.399C40.1535 76.399 33.8187 81.7512 35.1897 91.892Z" fill="url(#paint1_radial)"/>
<path d="M52.6341 91.892C51.5941 99.3568 51.7359 97.2441 52.965 97.7606C56.3215 99.1221 58.0707 98.3709 57.5034 91.1408C56.9361 83.6291 51.5941 77.7136 47.7175 76.399C47.7175 76.399 54.0523 81.7512 52.6341 91.892Z" fill="url(#paint2_radial)"/>
<path opacity="0.3" d="M43.8883 78.7935C46.7081 78.7935 48.9939 78.1629 48.9939 77.385C48.9939 76.6071 46.7081 75.9766 43.8883 75.9766C41.0685 75.9766 38.7826 76.6071 38.7826 77.385C38.7826 78.1629 41.0685 78.7935 43.8883 78.7935Z" fill="#DCE7F2"/>
<path d="M43.8883 77.3849C47.9613 77.3849 51.2631 74.1059 51.2631 70.061C51.2631 66.0161 47.9613 62.7371 43.8883 62.7371C39.8153 62.7371 36.5134 66.0161 36.5134 70.061C36.5134 74.1059 39.8153 77.3849 43.8883 77.3849Z" fill="url(#paint3_radial)"/>
<path d="M97.6396 91.2348C97.6396 98.7465 97.6396 97.8545 85.0173 97.8545C72.3949 97.8545 72.3949 98.7465 72.3949 91.2348C72.3949 83.723 78.0206 75.601 85.0173 75.601C92.0139 75.601 97.6396 83.723 97.6396 91.2348Z" fill="url(#paint4_radial)"/>
<path d="M76.366 91.892C77.406 99.3568 77.2642 97.2441 76.035 97.7606C72.6785 99.1221 70.9294 98.3709 71.4967 91.1408C72.0639 83.6291 77.406 77.7136 81.2825 76.399C81.2825 76.399 74.995 81.7512 76.366 91.892Z" fill="url(#paint5_radial)"/>
<path d="M93.8103 91.892C92.7702 99.3568 92.9121 97.2441 94.1412 97.7606C97.4977 99.1221 99.2469 98.3709 98.6796 91.1408C98.1123 83.6291 92.7702 77.7136 88.8937 76.399C88.8464 76.399 95.1812 81.7512 93.8103 91.892Z" fill="url(#paint6_radial)"/>
<path opacity="0.3" d="M85.0645 78.7935C87.8843 78.7935 90.1702 78.1629 90.1702 77.385C90.1702 76.6071 87.8843 75.9766 85.0645 75.9766C82.2447 75.9766 79.9589 76.6071 79.9589 77.385C79.9589 78.1629 82.2447 78.7935 85.0645 78.7935Z" fill="#DCE7F2"/>
<path d="M85.0646 77.3849C89.1376 77.3849 92.4394 74.1059 92.4394 70.061C92.4394 66.0161 89.1376 62.7371 85.0646 62.7371C80.9915 62.7371 77.6897 66.0161 77.6897 70.061C77.6897 74.1059 80.9915 77.3849 85.0646 77.3849Z" fill="url(#paint7_radial)"/>
<path d="M79.5806 92.5493C79.5806 101.516 79.5806 100.437 64.5 100.437C49.4193 100.437 49.4193 101.516 49.4193 92.5493C49.4193 83.5821 56.1796 73.8169 64.5 73.8169C72.8203 73.8169 79.5806 83.5352 79.5806 92.5493Z" fill="url(#paint8_radial)"/>
<path d="M54.0523 93.3005C55.2814 102.221 55.1396 99.7324 53.6741 100.296C49.6084 101.939 47.5756 101 48.2847 92.3615C48.9939 83.3944 55.3287 76.2582 59.9616 74.7559C59.9616 74.7559 52.3977 81.2347 54.0523 93.3005Z" fill="url(#paint9_radial)"/>
<path d="M74.9477 93.3005C73.7185 102.221 73.8604 99.7324 75.3259 100.296C79.3915 101.939 81.4243 101 80.7152 92.3615C80.0061 83.3944 73.6713 76.2582 69.0383 74.7559C69.0383 74.7559 76.6023 81.2347 74.9477 93.3005Z" fill="url(#paint10_radial)"/>
<path opacity="0.3" d="M64.4527 77.6667C67.8208 77.6667 70.5511 76.91 70.5511 75.9765C70.5511 75.0431 67.8208 74.2864 64.4527 74.2864C61.0846 74.2864 58.3542 75.0431 58.3542 75.9765C58.3542 76.91 61.0846 77.6667 64.4527 77.6667Z" fill="#DCE7F2"/>
<path d="M64.4527 75.9765C69.3351 75.9765 73.2931 72.0459 73.2931 67.1972C73.2931 62.3485 69.3351 58.4178 64.4527 58.4178C59.5703 58.4178 55.6123 62.3485 55.6123 67.1972C55.6123 72.0459 59.5703 75.9765 64.4527 75.9765Z" fill="url(#paint11_radial)"/>
<path d="M65.3036 25.7887C65.3036 27.0563 65.3036 26.8685 64.5 26.8685C63.6963 26.8685 63.6963 27.1033 63.6963 25.7887C63.6963 23.6761 64.3109 21 64.5 21C64.6891 21 65.3036 23.6761 65.3036 25.7887Z" fill="#996003"/>
<path d="M107 61.2348C107 61.2348 95.4177 54.709 80.4316 60.2959C80.4316 60.2959 76.1769 56.0236 65.7764 55.8827C54.4305 55.6949 48.663 60.4837 48.663 60.4837C33.6769 54.3334 22 61.2348 22 61.2348C22 43.77 41.0517 26.7278 64.5 26.7278C87.9483 26.7278 107 43.77 107 61.2348Z" fill="url(#paint12_radial)"/>
<path d="M48.5685 60.4367H48.6158C48.6158 60.4367 54.336 55.6949 65.7292 55.8358C76.1769 55.9766 80.3844 60.2489 80.3844 60.2489C80.4317 60.2489 80.4317 60.2489 80.4789 60.202C77.9734 36.0705 66.4383 26.7747 64.5001 26.7278C62.4673 26.7278 50.9795 36.6808 48.5685 60.4367Z" fill="url(#paint13_radial)"/>
</g>
<defs>
<filter id="filter0_d" x="14" y="13" width="117" height="111.964" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="8" dy="8"/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<radialGradient id="paint0_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(43.8859 86.7199) scale(14.2971 15.4067)">
<stop offset="0.0313947" stop-color="#FEFEFE"/>
<stop offset="0.1637" stop-color="#F5FAFE"/>
<stop offset="0.5541" stop-color="#E8F4FF"/>
<stop offset="1" stop-color="#BCC9CF"/>
</radialGradient>
<radialGradient id="paint1_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(34.7843 87.3025) scale(6.69912 20.167)">
<stop offset="0.0313947" stop-color="#FEFEFE"/>
<stop offset="0.1637" stop-color="#F5FAFE"/>
<stop offset="0.5541" stop-color="#E8F4FF"/>
<stop offset="1" stop-color="#BCC9CF"/>
</radialGradient>
<radialGradient id="paint2_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(53.017 87.3025) rotate(180) scale(6.69912 20.167)">
<stop offset="0.0313947" stop-color="#FEFEFE"/>
<stop offset="0.1637" stop-color="#F5FAFE"/>
<stop offset="0.5541" stop-color="#E8F4FF"/>
<stop offset="1" stop-color="#BCC9CF"/>
</radialGradient>
<radialGradient id="paint3_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(43.8866 70.0688) scale(10.3258 10.2545)">
<stop offset="0.0313947" stop-color="#FEFEFE"/>
<stop offset="0.1637" stop-color="#F5FAFE"/>
<stop offset="0.5541" stop-color="#E8F4FF"/>
<stop offset="1" stop-color="#BCC9CF"/>
</radialGradient>
<radialGradient id="paint4_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(85.0541 86.7199) scale(14.2971 15.4067)">
<stop offset="0.0313947" stop-color="#FEFEFE"/>
<stop offset="0.1637" stop-color="#F5FAFE"/>
<stop offset="0.5541" stop-color="#E8F4FF"/>
<stop offset="1" stop-color="#BCC9CF"/>
</radialGradient>
<radialGradient id="paint5_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(75.9494 87.3025) scale(6.69913 20.167)">
<stop offset="0.0313947" stop-color="#FEFEFE"/>
<stop offset="0.1637" stop-color="#F5FAFE"/>
<stop offset="0.5541" stop-color="#E8F4FF"/>
<stop offset="1" stop-color="#BCC9CF"/>
</radialGradient>
<radialGradient id="paint6_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(94.1819 87.3025) rotate(180) scale(6.69913 20.167)">
<stop offset="0.0313947" stop-color="#FEFEFE"/>
<stop offset="0.1637" stop-color="#F5FAFE"/>
<stop offset="0.5541" stop-color="#E8F4FF"/>
<stop offset="1" stop-color="#BCC9CF"/>
</radialGradient>
<radialGradient id="paint7_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(85.0542 70.0688) scale(10.3258 10.2545)">
<stop offset="0.0313947" stop-color="#FEFEFE"/>
<stop offset="0.1637" stop-color="#F5FAFE"/>
<stop offset="0.5541" stop-color="#E8F4FF"/>
<stop offset="1" stop-color="#BCC9CF"/>
</radialGradient>
<radialGradient id="paint8_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(64.4636 87.1449) scale(17.1182 18.4466)">
<stop offset="0.0313947" stop-color="#FEFEFE"/>
<stop offset="0.1637" stop-color="#F5FAFE"/>
<stop offset="0.5541" stop-color="#E8F4FF"/>
<stop offset="1" stop-color="#BCC9CF"/>
</radialGradient>
<radialGradient id="paint9_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(53.567 87.8422) scale(8.02096 24.1462)">
<stop offset="0.0313947" stop-color="#FEFEFE"/>
<stop offset="0.1637" stop-color="#F5FAFE"/>
<stop offset="0.5541" stop-color="#E8F4FF"/>
<stop offset="1" stop-color="#BCC9CF"/>
</radialGradient>
<radialGradient id="paint10_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(75.3993 87.8422) rotate(180) scale(8.02096 24.1462)">
<stop offset="0.0313947" stop-color="#FEFEFE"/>
<stop offset="0.1637" stop-color="#F5FAFE"/>
<stop offset="0.5541" stop-color="#E8F4FF"/>
<stop offset="1" stop-color="#BCC9CF"/>
</radialGradient>
<radialGradient id="paint11_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(64.464 67.2085) scale(12.3632 12.2778)">
<stop offset="0.0313947" stop-color="#FEFEFE"/>
<stop offset="0.1637" stop-color="#F5FAFE"/>
<stop offset="0.5541" stop-color="#E8F4FF"/>
<stop offset="1" stop-color="#BCC9CF"/>
</radialGradient>
<radialGradient id="paint12_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(64.5028 43.9884) rotate(0.844194) scale(53.9861 34.1231)">
<stop offset="0.0313947" stop-color="#DBBC83"/>
<stop offset="0.1637" stop-color="#DBAF4E"/>
<stop offset="0.5541" stop-color="#DC9700"/>
<stop offset="1" stop-color="#8C5A00"/>
</radialGradient>
<radialGradient id="paint13_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(64.5025 47.5809) rotate(89.219) scale(33.7175 17.4258)">
<stop offset="0.0313947" stop-color="#DBBC83"/>
<stop offset="0.1637" stop-color="#DBAF4E"/>
<stop offset="0.5541" stop-color="#DC9700"/>
<stop offset="1" stop-color="#8C5A00"/>
</radialGradient>
</defs>
</svg>
