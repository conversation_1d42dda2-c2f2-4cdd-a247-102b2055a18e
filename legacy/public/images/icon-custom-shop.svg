<svg width="130" height="130" viewBox="0 0 130 130" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d)">
<path d="M93.912 101.803H33.0222C28.6823 101.803 25.1315 98.2654 25.1315 93.9411V33.335C25.1315 29.0107 28.6823 25.4726 33.0222 25.4726H93.912C98.2519 25.4726 101.803 29.0107 101.803 33.335V94.0066C101.803 98.2654 98.2519 101.803 93.912 101.803Z" fill="url(#paint0_linear)" stroke="url(#paint1_linear)" stroke-width="0.5" stroke-miterlimit="10"/>
<path d="M93.9121 98.5274H33.0223C28.6824 98.5274 25.1316 94.9894 25.1316 90.665V29.9935C25.1316 25.6691 28.6824 22.131 33.0223 22.131H93.9121C98.252 22.131 101.803 25.6691 101.803 29.9935V90.665C101.803 94.9894 98.252 98.5274 93.9121 98.5274Z" fill="url(#paint2_linear)" stroke="url(#paint3_linear)" stroke-width="0.5" stroke-miterlimit="10"/>
<path d="M63.467 82.2785C53.6694 82.2785 43.9376 82.2785 34.1399 82.2785C34.0084 82.2785 33.8769 82.2785 33.7454 82.2785C33.5481 82.2785 33.4166 82.1474 33.4166 81.9509C33.4166 81.5577 33.4166 81.0991 33.4166 80.706C33.4166 80.3784 33.4824 80.3129 33.8112 80.3129C34.929 80.3129 35.9811 80.3129 37.099 80.3129C37.625 80.3129 37.625 80.3129 37.625 79.7887C37.625 73.1712 37.625 66.4881 37.625 59.8706C37.625 59.6085 37.625 59.3464 37.625 59.0844C37.625 58.7568 37.4935 58.5602 37.1647 58.4947C36.3099 58.2326 35.5866 57.8395 34.929 57.2498C33.9427 56.267 33.4166 55.1531 33.3509 53.7772C33.2851 52.4013 33.3509 51.0254 33.3509 49.715C33.3509 49.5839 33.4166 49.3874 33.5481 49.2563C35.2578 47.2252 36.9017 45.2596 38.6113 43.2285C39.2689 42.4423 39.9922 41.656 40.6498 40.8043C40.9128 40.4767 41.1758 40.4111 41.5704 40.4111C45.4499 40.4111 49.3953 40.4111 53.2749 40.4111C63.8616 40.4111 74.4482 40.4111 85.1007 40.4111C85.4952 40.4111 85.824 40.5422 86.087 40.8698C88.4542 43.6871 90.7557 46.439 93.1229 49.2563C93.2544 49.3874 93.3201 49.5839 93.3201 49.715C93.3201 51.2219 93.3859 52.7289 93.2544 54.1703C93.1229 55.9394 92.1365 57.1843 90.6242 58.036C90.2954 58.2326 89.9666 58.3636 89.5721 58.4291C89.2433 58.5602 89.1118 58.6912 89.1118 59.0844C89.1118 63.5397 89.1118 67.9951 89.1118 72.4504C89.1118 74.8747 89.1118 77.3644 89.1118 79.7887C89.1118 80.2473 89.1118 80.2473 89.5721 80.2473C90.6899 80.2473 91.8078 80.2473 92.9256 80.2473C93.2544 80.2473 93.3201 80.3129 93.3201 80.6405C93.3201 81.0336 93.3201 81.4267 93.3201 81.8198C93.3201 82.1474 93.1886 82.2129 92.9256 82.2129C91.9393 82.2129 90.9529 82.2129 89.9666 82.2129C81.2211 82.2785 72.344 82.2785 63.467 82.2785ZM63.467 42.3112C56.4969 42.3112 49.5268 42.3112 42.5567 42.3112C42.1622 42.3112 41.9649 42.4423 41.7019 42.7043C39.7949 44.9975 37.888 47.2907 35.9811 49.5184C35.5866 49.9771 35.3893 50.3702 35.4551 50.9599C35.5208 51.2219 35.4551 51.484 35.4551 51.8116C35.4551 52.5323 35.4551 53.3186 35.5208 54.0393C35.7181 55.6118 37.2305 56.7256 38.8086 56.5946C40.5183 56.4635 41.7676 55.1531 41.7676 53.3841C41.7676 52.3358 41.7676 51.2875 41.7676 50.1736C41.7676 49.7805 41.7676 49.715 42.1622 49.715C42.5567 49.715 42.8855 49.715 43.28 49.715C43.6745 49.715 43.7403 49.7805 43.7403 50.1736C43.7403 51.353 43.7403 52.5323 43.7403 53.7117C43.806 55.5463 45.9102 57.3808 48.2117 56.267C49.3953 55.6773 49.9871 54.629 49.9871 53.3186C49.9871 52.2703 49.9871 51.1564 49.9871 50.1081C49.9871 49.9115 50.0528 49.715 50.3159 49.715C50.7762 49.715 51.2364 49.715 51.6967 49.715C51.894 49.715 52.0255 49.846 52.0255 50.0426C52.0255 50.1736 52.0255 50.3047 52.0255 50.4357C52.0255 51.5495 51.9598 52.7289 52.0913 53.8427C52.2885 56.0049 54.7873 57.3153 56.6942 56.267C57.812 55.6118 58.3381 54.629 58.3381 53.3841C58.3381 52.3358 58.3381 51.2875 58.3381 50.1736C58.3381 49.9115 58.4038 49.7805 58.7326 49.7805C58.9956 49.7805 59.3244 49.7805 59.5874 49.7805C60.3765 49.7805 60.3107 49.715 60.3107 50.5667C60.3107 51.7461 60.245 52.9255 60.3765 54.0393C60.5738 56.0049 62.7437 57.3153 64.8479 56.3325C66.0315 55.7428 66.6233 54.6945 66.6233 53.3841C66.6233 52.3358 66.6233 51.2875 66.6233 50.2391C66.6233 49.7805 66.6233 49.7805 67.0836 49.7805C67.4781 49.7805 67.8069 49.7805 68.2014 49.7805C68.596 49.7805 68.6617 49.7805 68.6617 50.2391C68.6617 51.2875 68.6617 52.3358 68.6617 53.3841C68.6617 54.3014 68.9248 55.0876 69.5823 55.7428C70.5686 56.7256 72.1468 56.9222 73.3304 56.2015C74.4482 55.5463 74.9085 54.4979 74.9085 53.1875C74.9085 52.2047 74.9085 51.2219 74.9085 50.1736C74.9085 49.7805 74.9743 49.715 75.3688 49.715C75.7633 49.715 76.0921 49.715 76.4867 49.715C76.947 49.715 76.947 49.715 76.947 50.1736C76.947 51.2219 76.947 52.2703 76.947 53.3186C76.947 54.1703 77.1442 54.9566 77.736 55.5463C78.5908 56.4635 79.906 56.8567 81.1553 56.398C82.2074 56.0049 83.1938 54.8911 83.1938 53.5807C83.1938 52.4013 83.1938 51.2875 83.1938 50.1081C83.1938 49.7805 83.2595 49.6495 83.5883 49.6495C83.9828 49.6495 84.3116 49.6495 84.7061 49.6495C85.1664 49.6495 85.1664 49.6495 85.1664 50.1081C85.1664 51.2875 85.1007 52.5323 85.2322 53.7117C85.4294 55.9394 87.8624 57.2498 89.7693 56.2015C90.9529 55.6118 91.479 54.5635 91.479 53.2531C91.479 52.3358 91.479 51.4185 91.479 50.5012C91.479 50.1736 91.4132 49.9115 91.216 49.715C91.0187 49.4529 90.8214 49.2563 90.5584 48.9943C88.783 46.8976 87.0076 44.801 85.2979 42.7043C85.0349 42.3767 84.7061 42.2457 84.2458 42.2457C77.3415 42.3112 70.4371 42.3112 63.467 42.3112ZM56.1681 80.2473C61.4943 80.2473 66.8206 80.2473 72.081 80.2473C72.6071 80.2473 72.6071 80.2473 72.6071 79.7232C72.6071 74.416 72.6071 69.1744 72.6071 63.8673C72.6071 63.0811 72.6071 62.3604 72.6071 61.5741C72.6071 61.312 72.6728 61.181 73.0016 61.181C76.4209 61.181 79.7745 61.181 83.1938 61.181C83.7856 61.181 83.7856 61.181 83.7856 61.7707C83.7856 67.7985 83.7856 73.7608 83.7856 79.7887C83.7856 80.2473 83.7856 80.2473 84.2458 80.2473C84.9034 80.2473 85.6267 80.2473 86.2843 80.2473C87.4021 80.2473 87.2706 80.3784 87.2706 79.3301C87.2706 79.3301 87.2706 79.3301 87.2706 79.2645C87.2706 72.516 87.2706 65.7674 87.2706 59.0188C87.2706 58.9533 87.2706 58.8878 87.2706 58.7568C87.2706 58.5602 87.2049 58.4292 87.0076 58.3636C86.7446 58.2981 86.4815 58.2326 86.2843 58.1015C85.6267 57.8395 85.0349 57.3808 84.5089 56.7911C84.2458 56.5291 84.1801 56.5291 83.9828 56.7911C83.8513 56.9222 83.7856 57.0532 83.654 57.1187C81.4183 59.1499 78.1963 59.0188 76.1579 56.7256C75.9606 56.5291 75.8949 56.5291 75.6976 56.7256C73.7249 59.0844 69.9768 59.2154 67.8727 56.7256C67.6754 56.4635 67.5439 56.5291 67.3466 56.7256C65.3082 59.0844 61.6916 59.0844 59.6532 56.7911C59.3244 56.4635 59.3244 56.4635 58.9956 56.7911C58.1408 57.7084 57.1545 58.2981 55.9051 58.4291C54.0639 58.6912 52.4858 58.036 51.2364 56.6601C51.0392 56.4635 50.9734 56.4635 50.7762 56.6601C50.4474 56.9877 50.1186 57.3808 49.6583 57.6429C47.5541 59.0844 44.6609 58.6257 42.9512 56.7256C42.6882 56.398 42.6224 56.398 42.3594 56.7256C41.7019 57.4463 40.9785 57.9705 39.9922 58.2326C39.6634 58.2981 39.5977 58.4291 39.5977 58.7568C39.5977 61.4431 39.5977 64.1949 39.5977 66.8812C39.5977 71.14 39.5977 75.3333 39.5977 79.5921C39.5977 80.0508 39.5977 80.1163 40.1237 80.1163C40.2552 80.1163 40.321 80.1163 40.4525 80.1163C45.713 80.2473 50.9734 80.2473 56.1681 80.2473ZM81.7471 71.7297C81.7471 69.0434 81.7471 66.3571 81.7471 63.6708C81.7471 63.2121 81.7471 63.2121 81.2868 63.2121C79.2484 63.2121 77.1442 63.2121 75.1058 63.2121C74.5797 63.2121 74.5797 63.2121 74.5797 63.7363C74.5797 69.1089 74.5797 74.4816 74.5797 79.8542C74.5797 80.3129 74.5797 80.3129 75.04 80.3129C77.0785 80.3129 79.1826 80.3129 81.2211 80.3129C81.6814 80.3129 81.6814 80.3129 81.6814 79.8542C81.7471 77.1024 81.7471 74.416 81.7471 71.7297Z" fill="#843800"/>
<path d="M56.1681 61.181C60.3107 61.181 64.3876 61.181 68.5302 61.181C69.0562 61.181 69.0562 61.181 69.0562 61.7051C69.0562 67.1433 69.0562 72.5815 69.0562 77.9541C69.0562 78.0852 69.0562 78.2162 69.0562 78.3472C68.9905 78.4783 68.9247 78.5438 68.7932 78.6093C68.7275 78.6748 68.6617 78.6093 68.596 78.6093C60.3765 78.6093 52.157 78.6093 43.9375 78.6093C43.4115 78.6093 43.4115 78.6093 43.4115 78.0851C43.4115 72.647 43.4115 67.1433 43.4115 61.7051C43.4115 61.181 43.4115 61.181 43.9375 61.181C48.0144 61.181 52.0913 61.181 56.1681 61.181ZM56.1681 76.6437C59.6532 76.6437 63.0725 76.6437 66.5575 76.6437C67.0178 76.6437 67.0178 76.6437 67.0178 76.1851C67.0178 71.9918 67.0178 67.864 67.0178 63.6707C67.0178 63.2121 67.0178 63.2121 66.5575 63.2121C59.6532 63.2121 52.7488 63.2121 45.8445 63.2121C45.3842 63.2121 45.3842 63.2121 45.3842 63.6707C45.3842 67.7985 45.3842 71.9918 45.3842 76.1195C45.3842 76.5782 45.3842 76.5782 45.9102 76.5782C49.3295 76.6437 52.7488 76.6437 56.1681 76.6437Z" fill="#843800"/>
</g>
<defs>
<filter id="filter0_d" x="16.8815" y="13.881" width="109.171" height="112.172" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="8" dy="8"/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<linearGradient id="paint0_linear" x1="25.1643" y1="63.6405" x2="101.815" y2="63.6405" gradientUnits="userSpaceOnUse">
<stop offset="0.5161" stop-color="#DB8220"/>
<stop offset="1" stop-color="#B55400"/>
</linearGradient>
<linearGradient id="paint1_linear" x1="25" y1="63.6405" x2="101.979" y2="63.6405" gradientUnits="userSpaceOnUse">
<stop offset="0.3148" stop-color="#FA8829"/>
<stop offset="0.5383" stop-color="#F88728"/>
<stop offset="0.6585" stop-color="#F08226"/>
<stop offset="0.7541" stop-color="#E27922"/>
<stop offset="0.8367" stop-color="#CF6E1C"/>
<stop offset="0.911" stop-color="#B65E15"/>
<stop offset="0.9784" stop-color="#974C0C"/>
<stop offset="1" stop-color="#8C4509"/>
</linearGradient>
<linearGradient id="paint2_linear" x1="25.1645" y1="60.3518" x2="101.815" y2="60.3518" gradientUnits="userSpaceOnUse">
<stop offset="0.3763" stop-color="white"/>
<stop offset="1" stop-color="#D6A12D"/>
</linearGradient>
<linearGradient id="paint3_linear" x1="25.0001" y1="60.3518" x2="101.979" y2="60.3518" gradientUnits="userSpaceOnUse">
<stop offset="0.2996" stop-color="white"/>
<stop offset="1" stop-color="#B07720"/>
</linearGradient>
</defs>
</svg>
