<svg width="130" height="130" viewBox="0 0 130 130" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d)">
<path d="M94.8855 100.474H36.6942C33.2589 100.474 30.4482 97.6339 30.4482 94.1626V35.3618C30.4482 31.8905 33.2589 29.0504 36.6942 29.0504H94.8855C98.3207 29.0504 101.131 31.8905 101.131 35.3618V94.1626C101.131 97.6339 98.3207 100.474 94.8855 100.474Z" fill="url(#paint0_linear)"/>
<path d="M94.8855 100.474H36.6942C33.2589 100.474 30.4482 97.6339 30.4482 94.1626V35.3618C30.4482 31.8905 33.2589 29.0504 36.6942 29.0504H94.8855C98.3207 29.0504 101.131 31.8905 101.131 35.3618V94.1626C101.131 97.6339 98.3207 100.474 94.8855 100.474Z" stroke="url(#paint1_linear)" stroke-width="0.5" stroke-miterlimit="10"/>
</g>
<path d="M94.8855 99.6852H36.6942C33.2589 99.6852 30.4482 96.8451 30.4482 93.3738V34.6256C30.4482 31.1543 33.2589 28.3142 36.6942 28.3142H94.8855C98.3207 28.3142 101.131 31.1543 101.131 34.6256V93.4264C101.131 96.8451 98.3207 99.6852 94.8855 99.6852Z" fill="url(#paint2_linear)" stroke="url(#paint3_linear)" stroke-width="0.5" stroke-miterlimit="10"/>
<path d="M94.8855 98.9488H36.6942C33.2589 98.9488 30.4482 96.1087 30.4482 92.6375V33.8366C30.4482 30.3654 33.2589 27.5253 36.6942 27.5253H94.8855C98.3207 27.5253 101.131 30.3654 101.131 33.8366V92.6375C101.131 96.1087 98.3207 98.9488 94.8855 98.9488Z" fill="url(#paint4_linear)" stroke="url(#paint5_linear)" stroke-width="0.5" stroke-miterlimit="10"/>
<path d="M94.8855 98.1599H36.6942C33.2589 98.1599 30.4482 95.3198 30.4482 91.8485V33.0477C30.4482 29.5764 33.2589 26.7363 36.6942 26.7363H94.8855C98.3207 26.7363 101.131 29.5764 101.131 33.0477V91.8485C101.131 95.3198 98.3207 98.1599 94.8855 98.1599Z" fill="url(#paint6_linear)" stroke="url(#paint7_linear)" stroke-width="0.5" stroke-miterlimit="10"/>
<path d="M101.131 32.3114V91.1122C101.131 94.5834 98.3207 97.4236 94.8855 97.4236H36.6942C33.2589 97.4236 30.4482 94.5834 30.4482 91.1122V32.3114C30.4482 28.8401 33.2589 26 36.6942 26H94.8855C98.3207 26 101.131 28.8401 101.131 32.3114Z" fill="url(#paint8_linear)" stroke="url(#paint9_linear)" stroke-width="0.5" stroke-miterlimit="10"/>
<path d="M105.504 41.1473C104.827 49.5624 103.057 56.5575 101.079 64.7623C77.553 64.7623 53.9746 64.7623 30.4483 64.7623C28.4704 56.6101 26.7528 49.615 26.0241 41.1473C25.7118 37.676 28.4704 34.8359 32.3221 34.8359C54.5992 34.8359 76.9285 34.8359 99.2056 34.8359C103.057 34.8885 105.816 37.676 105.504 41.1473Z" fill="url(#paint10_linear)" stroke="url(#paint11_linear)" stroke-width="0.5" stroke-miterlimit="10"/>
<path d="M30.4482 64.7623V90.9019C30.4482 94.1102 33.0507 96.6873 36.1737 96.6873H95.3539C98.5289 96.6873 101.079 94.0576 101.079 90.9019V64.7623H30.4482Z" fill="url(#paint12_linear)" stroke="url(#paint13_linear)" stroke-width="0.5" stroke-miterlimit="10"/>
<path d="M61.9903 64.1837C62.4067 63.3948 62.719 62.7111 62.9272 62.1851C63.1874 61.6592 63.3436 61.0806 63.4477 60.5547C63.5518 60.0287 63.6038 59.4502 63.6559 58.8191C63.7079 58.2405 63.7079 57.5042 63.7079 56.6101C63.7079 55.4004 63.5518 54.1907 63.2395 53.0862C62.9272 51.9817 62.4067 50.9824 61.73 50.1409C61.0013 49.2994 60.0124 48.6157 58.8153 48.1423C57.6181 47.669 56.1607 47.406 54.4431 47.406C53.0898 47.406 51.8406 47.6164 50.7997 48.0371C49.7066 48.4579 48.8738 48.9838 48.2492 49.6676C47.5726 50.3513 47.1041 51.1402 46.8439 52.0869C46.5836 53.0336 46.5316 54.0855 46.7398 55.2426C46.948 56.5049 47.0521 57.0308 47.2603 58.1353C49.3423 58.1353 50.4353 58.1353 52.6734 58.1353C52.5173 56.9782 52.4132 56.3997 52.257 55.1374C52.1529 54.2433 52.3091 53.5596 52.7775 53.1388C53.246 52.7181 53.9226 52.4551 54.8595 52.5077C55.5882 52.5077 56.2128 52.6655 56.6292 52.9284C57.0456 53.1914 57.3579 53.507 57.5661 53.8752C57.7743 54.2433 57.9304 54.7167 57.9825 55.2426C58.0345 55.7686 58.0866 56.2419 58.0866 56.6627C58.1386 57.662 58.1386 58.4509 58.1386 59.1346C58.0866 59.8183 57.8784 60.5547 57.4099 61.4488C56.7853 62.7637 56.473 63.3948 55.9005 64.7622C58.1907 64.7622 59.3358 64.7622 61.678 64.7622C61.7821 64.5519 61.8341 64.3941 61.9903 64.1837Z" fill="#B2370D"/>
<path d="M81.3007 62.2377C80.3638 61.1332 78.9585 60.6598 77.1367 60.6598C76.1998 60.6598 75.2629 60.8176 74.4822 61.1858C73.6494 61.5013 73.0248 61.9221 72.5043 62.3955C72.7646 58.3983 72.8166 56.5574 73.0769 52.6128C77.8654 52.6128 80.5199 52.6128 84.5798 52.6128C84.9441 50.6668 85.1003 49.6675 85.4126 47.6689C79.583 47.6689 73.7535 47.6689 67.924 47.6689C67.8719 53.3492 67.8199 59.0294 67.7678 64.7096C73.5453 64.7096 76.4601 64.7096 82.2376 64.7096C82.1335 63.6577 81.8212 62.8688 81.3007 62.2377Z" fill="#B2370D"/>
<path d="M48.3013 84.2222H63.7079V78.9628H54.3911L61.678 64.7622H55.9005L48.3013 79.2783V84.2222Z" fill="#E2591B"/>
<path d="M72.2961 67.3919C72.3481 67.3393 72.4522 67.129 72.6084 66.8134C72.7125 66.5504 72.8686 66.3401 73.0768 66.1297C73.285 65.9193 73.5453 65.7615 73.8576 65.6037C74.1699 65.4459 74.5342 65.3933 74.9506 65.3933C75.6793 65.3933 76.2518 65.6563 76.7203 66.1823C77.1887 66.7082 77.3969 67.2868 77.3969 68.0231V76.596C77.3969 77.4375 77.1887 78.1213 76.7723 78.5946C76.3559 79.068 75.7313 79.3309 74.9506 79.3309C74.1699 79.3309 73.5453 79.068 73.0248 78.5946C72.5563 78.1213 72.2961 77.3849 72.2961 76.5434V75.2285H67.1432V76.7538C67.1432 77.9635 67.3514 79.068 67.7678 80.0147C68.1842 80.9614 68.7567 81.8029 69.4854 82.4866C70.2141 83.1703 70.9948 83.6963 71.9317 84.0645C72.8686 84.4326 73.8576 84.5904 74.8465 84.5904C75.8354 84.5904 76.8244 84.4326 77.7613 84.0645C78.6982 83.6963 79.531 83.1703 80.2076 82.4866C80.9363 81.8029 81.4568 80.9614 81.9252 80.0147C82.3416 79.068 82.5498 77.9635 82.5498 76.7538V67.2342C82.5498 66.2875 82.4457 65.4985 82.2375 64.7622H67.7678V67.3919H72.2961Z" fill="#E2591B"/>
<path d="M59.4919 89.3767V90.6915H61.1054V90.9019H59.4919V92.2694H59.2837V89.2189H61.3136V89.4293H59.4919V89.3767Z" fill="black"/>
<path d="M64.1243 92.2694L63.3956 91.2701C63.2915 91.2701 63.1874 91.2701 63.0833 91.2701H62.1984V92.2694H61.9902V89.2189H63.0833C63.864 89.2189 64.2804 89.587 64.2804 90.2708C64.2804 90.7441 64.0202 91.1123 63.5517 91.2175L64.2804 92.2694H64.1243ZM63.0833 91.0597C63.7079 91.0597 64.0722 90.7441 64.0722 90.2182C64.0722 89.6922 63.7079 89.3767 63.0833 89.3767H62.1984V91.0071H63.0833V91.0597Z" fill="black"/>
<path d="M65.1133 89.2189H65.3215V92.2694H65.1133V89.2189Z" fill="black"/>
<path d="M66.3624 89.2189H67.5596C68.4965 89.2189 69.1731 89.85 69.1731 90.7441C69.1731 91.6382 68.5485 92.2694 67.5596 92.2694H66.3624V89.2189ZM67.5596 92.059C68.3924 92.059 68.9649 91.533 68.9649 90.7441C68.9649 89.9552 68.3924 89.4293 67.5596 89.4293H66.6227V92.059H67.5596Z" fill="black"/>
<path d="M71.7755 91.3752H70.0058L69.6415 92.2168H69.3812L70.7866 89.1663H70.9948L72.4001 92.2168H72.1398L71.7755 91.3752ZM71.6714 91.2175L70.8907 89.4818L70.1099 91.2175H71.6714Z" fill="black"/>
<path d="M73.7014 91.2175V92.2694H73.4932V91.2175L72.296 89.2189H72.5563L73.6493 91.0071L74.7423 89.2189H74.9505L73.7014 91.2175Z" fill="black"/>
<defs>
<filter id="filter0_d" x="22.1982" y="20.8004" width="103.183" height="103.924" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="8" dy="8"/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<linearGradient id="paint0_linear" x1="65.7748" y1="100.479" x2="65.7748" y2="29.059" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#BFD9D9"/>
</linearGradient>
<linearGradient id="paint1_linear" x1="65.7748" y1="28.7961" x2="65.7748" y2="100.742" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#D7DBDB"/>
</linearGradient>
<linearGradient id="paint2_linear" x1="65.7748" y1="99.7087" x2="65.7748" y2="28.2889" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#BFD9D9"/>
</linearGradient>
<linearGradient id="paint3_linear" x1="65.7748" y1="28.0259" x2="65.7748" y2="99.9717" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#D7DBDB"/>
</linearGradient>
<linearGradient id="paint4_linear" x1="65.7748" y1="98.9501" x2="65.7748" y2="27.5303" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#BFD9D9"/>
</linearGradient>
<linearGradient id="paint5_linear" x1="65.7748" y1="27.2673" x2="65.7748" y2="99.2131" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#D7DBDB"/>
</linearGradient>
<linearGradient id="paint6_linear" x1="65.7748" y1="98.1539" x2="65.7748" y2="26.734" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#BFD9D9"/>
</linearGradient>
<linearGradient id="paint7_linear" x1="65.7748" y1="26.471" x2="65.7748" y2="98.4169" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#D7DBDB"/>
</linearGradient>
<linearGradient id="paint8_linear" x1="65.7748" y1="97.4199" x2="65.7748" y2="26" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#E6F5F5"/>
</linearGradient>
<linearGradient id="paint9_linear" x1="65.7748" y1="25.737" x2="65.7748" y2="97.6829" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#D7DBDB"/>
</linearGradient>
<linearGradient id="paint10_linear" x1="65.7748" y1="64.7613" x2="65.7748" y2="34.8624" gradientUnits="userSpaceOnUse">
<stop stop-color="#AFC2C2"/>
<stop offset="1" stop-color="#96ABAB"/>
</linearGradient>
<linearGradient id="paint11_linear" x1="65.7748" y1="34.5995" x2="65.7748" y2="65.0243" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.3689" stop-color="#8E9E9E"/>
</linearGradient>
<linearGradient id="paint12_linear" x1="65.7748" y1="96.7169" x2="65.7748" y2="64.7691" gradientUnits="userSpaceOnUse">
<stop offset="0.5161" stop-color="white"/>
<stop offset="1" stop-color="#C1D6D6"/>
</linearGradient>
<linearGradient id="paint13_linear" x1="30.1748" y1="80.743" x2="101.375" y2="80.743" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#E6EBEB"/>
</linearGradient>
</defs>
</svg>
