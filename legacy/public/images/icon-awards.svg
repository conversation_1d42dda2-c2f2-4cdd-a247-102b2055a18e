<svg width="130" height="132" viewBox="0 0 130 132" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d)">
<path d="M37.5968 100.022L48.163 98.1732L54.2238 107.147C54.492 107.093 54.7065 106.875 54.8138 106.603L84.0987 36.1163L66.8281 28.7739L37.5432 99.261C37.4359 99.5329 37.4896 99.8049 37.5968 100.022Z" fill="url(#paint0_linear)"/>
<path d="M49.6647 94.6381L42.424 95.8891L68.5444 33.0164L79.8614 37.8569L53.7946 100.73L49.6647 94.6381Z" fill="url(#paint1_linear)"/>
<path d="M89.7819 100.023L79.2157 98.1734L73.1013 107.147C72.8331 107.093 72.6186 106.875 72.5113 106.604L43.28 36.1165L60.5506 28.7197L89.8355 99.2068C89.8891 99.5331 89.8891 99.805 89.7819 100.023Z" fill="url(#paint2_linear)"/>
<path d="M77.7139 94.6381L84.9011 95.8891L58.8343 33.0164L47.4636 37.8569L73.584 100.73L77.7139 94.6381Z" fill="url(#paint3_linear)"/>
<path d="M93.5375 54.4611C93.5375 51.0891 98.2574 46.9556 97.2383 43.9098C96.2192 40.7553 89.9976 40.2114 88.1203 37.6008C86.1894 34.9358 87.6376 28.7899 85.0095 26.8319C82.3813 24.874 77.0714 28.1373 73.9606 27.1039C70.9034 26.1249 68.5434 20.251 65.1644 20.251C61.839 20.251 59.4254 26.0705 56.4218 27.1039C53.2574 28.1373 47.9475 24.874 45.373 26.7776C42.7448 28.7355 44.193 34.8814 42.2621 37.5464C40.3849 40.2114 34.1632 40.7009 33.1441 43.8554C32.1787 46.9012 36.8449 51.0347 36.8449 54.4067C36.8449 57.7788 32.125 61.9123 33.1441 64.958C34.1632 68.1126 40.3849 68.6564 42.2621 71.2671C44.193 73.9321 42.7448 80.0779 45.373 82.0359C48.0011 83.9395 53.311 80.6762 56.4218 81.7096C59.4254 82.6886 61.839 88.5625 65.1644 88.5625C68.4898 88.5625 70.9034 82.743 73.907 81.7096C77.0178 80.6762 82.3813 83.9395 84.9558 82.0359C87.584 80.0779 86.1358 73.9321 88.0667 71.2671C89.9439 68.602 96.1656 68.1126 97.1847 64.958C98.2574 61.9667 93.5375 57.8332 93.5375 54.4611Z" fill="url(#paint4_linear)"/>
<path d="M93.5375 53.2101C93.5375 49.8381 98.2574 45.7046 97.2383 42.6588C96.2192 39.5043 89.9976 38.9604 88.1203 36.3498C86.1894 33.6848 87.6376 27.5389 85.0095 25.581C82.3813 23.6774 77.0714 26.9407 73.9606 25.9073C70.9034 24.8739 68.5434 19 65.1644 19C61.839 19 59.4254 24.8739 56.4218 25.8529C53.311 26.8863 47.9475 23.623 45.373 25.5266C42.7448 27.4846 44.193 33.6304 42.2621 36.2954C40.3849 38.9604 34.1632 39.4499 33.1441 42.6045C32.1787 45.6502 36.8449 49.7837 36.8449 53.1558C36.8449 56.5278 32.125 60.6613 33.1441 63.7071C34.1632 66.8616 40.3849 67.4055 42.2621 70.0161C44.193 72.6811 42.7448 78.827 45.373 80.7849C48.0011 82.6885 53.311 79.4252 56.4218 80.4586C59.4254 81.4376 61.839 87.3115 65.1644 87.3115C68.4898 87.3115 70.9034 81.492 73.907 80.4586C77.0178 79.4252 82.3813 82.6885 84.9558 80.7849C87.584 78.827 86.1358 72.6811 88.0667 70.0161C89.9439 67.3511 96.1656 66.8616 97.1847 63.7071C98.2574 60.7157 93.5375 56.6366 93.5375 53.2101Z" fill="url(#paint5_linear)"/>
<path d="M93.5375 53.2101C93.5375 49.8381 98.2574 45.7046 97.2383 42.6588C96.2192 39.5043 89.9976 38.9604 88.1203 36.3498C86.1894 33.6848 87.6376 27.5389 85.0095 25.581C82.3813 23.6774 77.0714 26.9407 73.9606 25.9073C70.9034 24.8739 68.5434 19 65.1644 19C61.839 19 59.4254 24.8739 56.4218 25.8529C53.311 26.8863 47.9475 23.623 45.373 25.5266C42.7448 27.4846 44.193 33.6304 42.2621 36.2954C40.3849 38.9604 34.1632 39.4499 33.1441 42.6045C32.1787 45.6502 36.8449 49.7837 36.8449 53.1558C36.8449 56.5278 32.125 60.6613 33.1441 63.7071C34.1632 66.8616 40.3849 67.4055 42.2621 70.0161C44.193 72.6811 42.7448 78.827 45.373 80.7849C48.0011 82.6885 53.311 79.4252 56.4218 80.4586C59.4254 81.4376 61.839 87.3115 65.1644 87.3115C68.4898 87.3115 70.9034 81.492 73.907 80.4586C77.0178 79.4252 82.3813 82.6885 84.9558 80.7849C87.584 78.827 86.1358 72.6811 88.0667 70.0161C89.9439 67.3511 96.1656 66.8616 97.1847 63.7071C98.2574 60.7157 93.5375 56.6366 93.5375 53.2101Z" stroke="url(#paint6_linear)" stroke-width="0.5" stroke-miterlimit="10"/>
<path d="M65.1645 74.639C76.4801 74.639 85.6532 65.3371 85.6532 53.8627C85.6532 42.3883 76.4801 33.0864 65.1645 33.0864C53.8489 33.0864 44.6758 42.3883 44.6758 53.8627C44.6758 65.3371 53.8489 74.639 65.1645 74.639Z" fill="url(#paint7_linear)" stroke="url(#paint8_linear)" stroke-width="0.5" stroke-miterlimit="10"/>
<path d="M65.1644 64.3051C70.763 64.3051 75.3015 59.7028 75.3015 54.0257C75.3015 48.3486 70.763 43.7463 65.1644 43.7463C59.5659 43.7463 55.0273 48.3486 55.0273 54.0257C55.0273 59.7028 59.5659 64.3051 65.1644 64.3051Z" fill="url(#paint9_linear)"/>
<path d="M65.1644 63.9244C70.763 63.9244 75.3015 59.3222 75.3015 53.6451C75.3015 47.9679 70.763 43.3657 65.1644 43.3657C59.5659 43.3657 55.0273 47.9679 55.0273 53.6451C55.0273 59.3222 59.5659 63.9244 65.1644 63.9244Z" fill="url(#paint10_linear)" stroke="url(#paint11_linear)" stroke-width="0.5" stroke-miterlimit="10"/>
</g>
<defs>
<filter id="filter0_d" x="24.75" y="10.75" width="96.8825" height="120.397" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="8" dy="8"/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<linearGradient id="paint0_linear" x1="75.4618" y1="32.4376" x2="45.2139" y2="103.297" gradientUnits="userSpaceOnUse">
<stop offset="0.3355" stop-color="#5E4C06"/>
<stop offset="0.9924" stop-color="#E8C239"/>
</linearGradient>
<linearGradient id="paint1_linear" x1="74.2131" y1="35.4456" x2="47.4945" y2="98.0375" gradientUnits="userSpaceOnUse">
<stop offset="0.692708" stop-color="#470100"/>
<stop offset="0.864583" stop-color="#D30202"/>
<stop offset="1" stop-color="#D34000"/>
</linearGradient>
<linearGradient id="paint2_linear" x1="51.8934" y1="32.4377" x2="82.1413" y2="103.297" gradientUnits="userSpaceOnUse">
<stop offset="0.3355" stop-color="#5E4C06"/>
<stop offset="0.9924" stop-color="#E8C239"/>
</linearGradient>
<linearGradient id="paint3_linear" x1="53.1421" y1="35.4455" x2="79.8607" y2="98.0374" gradientUnits="userSpaceOnUse">
<stop offset="0.640625" stop-color="#470100"/>
<stop offset="0.838542" stop-color="#D30202"/>
<stop offset="1" stop-color="#D34000"/>
</linearGradient>
<linearGradient id="paint4_linear" x1="45.5053" y1="34.5082" x2="93.3173" y2="81.6584" gradientUnits="userSpaceOnUse">
<stop stop-color="#A87221"/>
<stop offset="1" stop-color="#A07708"/>
</linearGradient>
<linearGradient id="paint5_linear" x1="45.5053" y1="33.2811" x2="93.3173" y2="80.4313" gradientUnits="userSpaceOnUse">
<stop stop-color="#FBE053"/>
<stop offset="1" stop-color="#B1820A"/>
</linearGradient>
<linearGradient id="paint6_linear" x1="41.4985" y1="29.2181" x2="89.5028" y2="76.558" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFFAEA"/>
<stop offset="1" stop-color="#C69928"/>
</linearGradient>
<linearGradient id="paint7_linear" x1="82.2732" y1="53.8361" x2="41.2643" y2="53.8361" gradientUnits="userSpaceOnUse">
<stop stop-color="#EACE39"/>
<stop offset="1" stop-color="#B1820A"/>
</linearGradient>
<linearGradient id="paint8_linear" x1="50.5772" y1="39.0375" x2="80.1713" y2="68.222" gradientUnits="userSpaceOnUse">
<stop stop-color="#FDE590"/>
<stop offset="1" stop-color="#B1820A"/>
</linearGradient>
<linearGradient id="paint9_linear" x1="69.0752" y1="46.4135" x2="59.5058" y2="64.5833" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADAD97"/>
<stop offset="1" stop-color="#705B0C"/>
</linearGradient>
<linearGradient id="paint10_linear" x1="69.0752" y1="46.0121" x2="59.5058" y2="64.1821" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFFEEE"/>
<stop offset="1" stop-color="#A3A095"/>
</linearGradient>
<linearGradient id="paint11_linear" x1="55.2308" y1="50.9345" x2="75.1474" y2="56.1972" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFFEDE"/>
<stop offset="0.7427" stop-color="#BF9C1D"/>
</linearGradient>
</defs>
</svg>
