@import 'theme';
@import 'mixins';

.cardUser{
  width: calc(33% - 3.2rem);
  margin: 0 1.6rem 1.6rem 0;
  padding: 1.6rem;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: flex-start;
  text-align: center;
  background: $bright-color;
  border-radius: .8rem;
  box-shadow: 6px 6px 10px rgba(0, 0, 0, 0.2);

  .photo{
    width: 10rem;
    height: 10rem;  
    border-radius: 50%;
    overflow: hidden;   
    margin: 0 1.6rem 1.6rem 0;
    display: block;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: top center;
    background-color: $mid-gray;
  }

  .cardBody{
    width: calc(100% - 11.6rem);
    max-width: calc(100% - 11.6rem);
    display: block;
    text-align: left;   
    margin-top: .8rem;    
    
    h3{
      font-family: $display-font;
      font-size: 1.8rem;
      font-weight: 600;
      color: $base-color;
      margin: 0 0 1.2rem 0;
    }
  
    p{
      font-family: $display-font;
      font-size: 1.4rem;
      line-height: 2.1rem;
      font-weight: 400;
      color: $base-color;
      margin: 0;
      word-break: break-all;
  
      strong{
        font-weight: 600;
      }
    }
  }    
}

@include responsive(desktop){
  .cardUser{
    width: calc(33% - 3.2rem);
  }
}

@include responsive(tablet){
  .cardUser{
    width: calc(50% - 3.2rem);
  }
}

@include responsive(mobile){
  .cardUser{
    width: calc(100% - 3.2rem);
  }
}