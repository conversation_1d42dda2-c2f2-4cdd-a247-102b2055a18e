@import 'theme';
@import 'mixins';

.heading {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  text-align: center;

  h3 {
    display: block;
    width: 100%;
    font-size: 2.4rem;
    font-family: $display-font;
    font-weight: 600;
    margin: 0;
  }

  h1 {
    width: 100%;
    text-align: center;
    margin-bottom: 0;
  }
}

.content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  text-align: center;

  .banner {
    width: 100%;
    height: auto;
    display: block;
    margin-top: 3.2rem;

    img {
      width: 100%;
      max-width: 100%;
      height: auto;
      display: block;
      border-radius: 0.8rem;
      margin: 0 0 3.2rem 0;
    }
  }

  h3 {
    display: block;
    width: 100%;
    font-size: 2.4rem;
    font-family: $display-font;
    font-weight: 600;
    margin: 3.2rem 0;
    color: $highlight-color;
  }

  h1 {
    width: 100%;
    text-align: center;
    margin-bottom: 0;
  }

  p {
    font-size: 1.6rem;
    line-height: 2.4rem;
    font-family: $body-font;
  }
}

.list {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-end;
  position: relative;

  &:before {
    content: '';
    height: 32%;
    width: 0.1rem;
    background: transparent;
    position: absolute;
    border-left: 0.3rem dashed $mid-gray;
    left: 3.2rem;
    top: 10%;
  }

  h3 {
    font-family: $display-font;
    font-size: 2.4rem;
    color: $base-color;
  }

  h4 {
    font-family: $display-font;
    font-size: 1.8rem;
    color: $base-color;
    margin-bottom: 0;
  }

  ul {
    list-style: none;
    padding: 0;
    font-family: $display-font;
    font-size: 1.6rem;

    li {
      width: 100%;
      display: flex;
      align-items: center;
      height: 4rem;
      line-height: 4rem;
      color: $highlight-dark;

      svg {
        fill: $mid-gray;
        margin-right: 0.6rem;
      }
    }
  }

  .innerList {
    li {
      a {
        font-weight: 800;
        &:hover {
          text-decoration: underline;
        }
      }
    }
    .checked {
      svg {
        fill: $highlight-color;
      }
    }
  }
}

.step {
  width: calc(100% - 6.4rem);
  background: white;
  border: 0.1rem solid $mid-gray;
  border-radius: 0.8rem;
  padding: 1.6rem;
  margin: 3.2rem 0;
  position: relative;

  > span {
    width: 2.4rem;
    height: 2.4rem;
    display: flex;
    border-radius: 50%;
    justify-content: center;
    align-items: center;
    font-family: $display-font;
    font-size: 1.6rem;
    font-weight: 700;
    color: $highlight-color;
    background: white;
    border: 0.1rem solid $mid-gray;
    position: absolute;
    left: -4.4rem;
    top: 10%;
  }

  h4 {
    margin-top: 0;
  }
}
