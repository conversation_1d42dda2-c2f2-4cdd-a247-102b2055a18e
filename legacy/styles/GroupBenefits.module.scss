@import 'theme';
@import 'mixins';

.heading{
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 3.2rem;
  padding: 3.2rem;
  background: $bright-color; 
  border-radius: .8rem;

  h2{
    width: 100%;
    display: block;
    padding-right: 3.2rem;
    font-family: $display-font;
    color: $highlight-dark;
    font-size: 2.8rem;
    margin-bottom: 1.6rem;
    margin-top: 0;

  }

  p{
    font-family: $body-font;
    font-size: 1.6rem;
    color: $base-color;
    margin: 0 0 2.4rem 0;

    a{
      color: $highlight-dark;
      text-decoration: underline;
    }

    &:last-child{
      margin-bottom: 0;
    }
  }
}

.list{
  width: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;

  .ax_card_list{
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;
    
    >div{
      margin: 1.6rem 1.6rem 1.6rem 0 ;
      align-self: stretch;
      >div{
        height: 100%;
      }
    }
  }

  @include responsive(desktop){
    .ax_card_list {
      > div {
        width: calc(25% - 1.6rem);
        display: flex;
        align-self: stretch;
      }
    }
  }
  
  @include responsive(tablet){
    .ax_card_list {
      > div {
        width: calc(33% - 1.6rem);
        display: block;
      }
    }
  }
  
  @include responsive(mobile){
    .ax_card_list {
      > div {
        margin: 0 0 3.2rem 0;
        width: 100%;
        display: block;
      }
    }
  }
}
