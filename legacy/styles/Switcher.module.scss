@import 'theme';
@import 'mixins';

.switcher {
  display: flex;
  flex-direction: column;

  label {
    width: 100% !important;
    display: block !important;
    font-family: $display-font !important;
    font-size: 1.4rem !important;
    line-height: 1.4rem !important;
    color: $base-color !important;
    font-weight: 600 !important;
    margin-bottom: 0.4rem !important;
  }

  input {
    width: 4.8rem !important;
    height: 2.4rem !important;
    transition: all 0.2s ease-out !important;
    background: transparent !important;
    appearance: none !important;
    position: relative !important;
    z-index: 3 !important;
    margin: 0 !important;
    cursor: pointer !important;
    border: none !important;

    &:before {
      content: 'OFF\00a0' !important;
      width: 5.2rem !important;
      height: 2.4rem !important;
      border-radius: 1.2rem !important;
      background: red !important;
      position: absolute !important;
      top: 0 !important;
      left: 0 !important;
      z-index: 1 !important;
      color: #fff !important;
      font-weight: 600 !important;
      text-align: right !important;
      text-indent: 0.4rem !important;
      line-height: 2.4rem !important;
      font-size: 1.2rem !important;
    }

    &:after {
      content: '' !important;
      width: 2.2rem !important;
      height: 2.2rem !important;
      border-radius: 50% !important;
      background: #f2f2f2 !important;
      display: block !important;
      position: absolute !important;
      left: 0.1rem !important;
      top: 0.1rem !important;
      z-index: 2 !important;
      transition: all 0.2s ease-out !important;
    }
  }

  input[checked] {
    &:before {
      content: 'ON' !important;
      background: $highlight-color !important;
      color: #fff !important;
      font-weight: 600 !important;
      text-align: left !important;
      text-indent: 0.4rem !important;
      line-height: 2.4rem !important;
      font-size: 1.2rem !important;
    }

    &:after {
      content: '' !important;
      left: 2.9rem !important;
    }
  }
}

.switcherBox {
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  justify-content: flex-start;
  position: relative;

  .switcherContainer {
    width: 11.2rem;
    position: relative;
  }

  .leftBox,
  .rightBox {
    font-size: 1.6rem;
    line-height: 1.6rem;
    text-align: center;
    width: 1.6rem;
    height: 1.6rem;
    border-radius: 0.2rem;
    border: 0.2rem solid $mid-gray;
    background: $light-gray;
    display: block;
    position: absolute;
    z-index: 0;
    top: 0.4rem;

    span {
      position: absolute;

      top: -0.2rem;
      color: #000;
      font-family: $display-font;
      font-weight: 700;
      font-size: 1.4rem;
      padding-left: 0.4rem;
    }
  }

  .leftBox {
    left: 0rem;
  }

  .rightBox {
    right: 3.8rem;
    span {
      right: -3.2rem;
    }
  }

  .leftBox {
    left: 0rem;
    span {
      left: 1.2rem;
    }
  }

  label {
    width: 100%;
    display: block;
    font-family: $display-font;
    font-size: 1.4rem;
    line-height: 1.4rem;
    color: $highlight-dark;
    font-weight: 600;
    margin-bottom: 0.4rem;
    pointer-events: none;
  }

  input {
    width: 10rem !important;
    height: 2.4rem !important;
    transition: all 0.2s ease-out !important;
    background: transparent !important;
    appearance: none !important;
    position: relative !important;
    z-index: 3 !important;
    margin: 0 !important;
    cursor: pointer !important;
    border: none !important;

    &:before {
      content: 'OFF\00a0';
      width: 9.6rem;
      height: 2.4rem;
      border-radius: 0.2rem;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
      color: #000;
      font-weight: 600;
      text-align: right;
      text-indent: 0.4rem;
      line-height: 2.4rem;
      font-size: 1.2rem;
    }

    &:after {
      content: '\2713';
      font-size: 1.6rem;
      line-height: 1.6rem;
      text-align: center;
      width: 1.2rem;
      height: 1.2rem;
      border-radius: 0.2rem;
      background: transparent;
      border: none;
      display: block;
      position: absolute;
      right: 2.8rem;
      top: 0.5rem;
      z-index: 50;
    }
  }

  input[checked] {
    &:before {
      content: 'ON';
      background: transparent;
      color: #000;
      font-weight: 600;
      text-align: left;
      text-indent: 0.4rem;
      line-height: 2.4rem;
      font-size: 1.2rem;
    }

    &:after {
      content: '\2713';
      left: 0.2rem;
    }
  }

  > p {
    width: calc(100% - 12rem);
  }
}

.yeNoSwitcherBox {
  input {
    &:before {
      content: '' !important;
    }
  }

  input[checked] {
    &:before {
      content: '' !important;
    }
  }
}

.switcherNull {
  input {
    &:before,
    &:after {
      content: '' !important;
    }
  }
}

.labelTop {
  margin-bottom: 1.2rem;
  label {
    margin-bottom: 0;
  }
}

.yeNoSwitcher {
  input {
    &:before {
      content: 'NO\00a0' !important;
    }
  }

  input[checked] {
    &:before {
      content: 'YES' !important;
    }
  }
}

.labelLeft {
  flex-direction: row;
  justify-content: flex-start;

  label {
    width: auto;
    margin-right: 0.8rem;
    display: inline-block;
    text-align: left;
    line-height: 2.2rem;
  }
}
