@import 'theme';
@import 'mixins';

.contentContainer {
  width: 100%;
  display: flex;
  align-items: flex-start;
  flex-wrap: wrap;
  justify-content: space-between;
}

.pageBanner{
  width: 100%;
  display: flex;
  position: relative;
  border-radius: .8rem .8rem 0 0;
  overflow: hidden;
  margin: 1.2rem 0 0 0;
  
  > img{
    width: 100%;
    max-width: 100%;
    height: auto;
  }

  .pageThumb{
    position: absolute;
    width: 28rem;
    height: 28rem;
    display: flex;
    justify-content: center;
    align-items: center;
    top: calc(50% - 14rem);
    left: 6.4rem;
    background: $bright-color;
    padding: 3.2rem;
    border-radius: 1.6rem;
    box-shadow: $shadow-large;

    img {
      width: 100%;
      max-width: 100%;
      height: auto;
    }
  }
}

.pageContent{
  width: 100%;
  display: block;
  padding: 3.2rem;
  border-radius: .8rem;
  background: $bright-color;
  margin: 1.2rem 0 0 0;

  h1, h2, h3, h4, h5, h6 {
    font-family: $display-font;
    color: $base-color;
  }

  h1{
    font-size: 2.4rem;
    line-height: 3.2rem;
  }

  h2{
    font-size: 2.1rem;
    line-height: 2.8rem;
  }

  h3{
    font-size: 1.8rem;
    line-height: 2.4rem;
  }

  h4{
    font-size: 1.6rem;
    line-height: 2.1rem;
  }

  h5{
    font-size: 1.4rem;
    line-height: 1.8rem;
  }

  h6{
    font-size: 1.2rem;
    line-height: 1.6rem;
  }

  p{
    font-family: $body-font;
    color: $base-color;
    font-size: 1.6rem;
    line-height: 2.1rem;
  }

  a{
    color: $highlight-dark;
    text-decoration: underline;

    &:hover{
      cursor: pointer;
    }
  }
  
}

.mediaList {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  justify-content: flex-start;
  margin-top: 3.2rem;

  .card{
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-bottom: 3.2rem;
    margin-right: 1.6rem;
    padding: 1.6rem;
    background: $bright-color;
    border-radius: .6rem;
    margin-bottom: 1.6rem;

    a{    
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
  
      img{      
        display: block;
        width: 100%;
        max-width: 100%;
        height: auto;
      }
    }

    h3{
      font-family: $display-font;
      font-size: 1.4rem;
      font-weight: 600;
      text-transform: capitalize;
      margin-bottom: 0;
      text-align: center;
      margin-top: auto;
      padding-top: 1.6rem;
    }

    .btn {
      margin-top: 1.6rem;
      background: $highlight-color;
      color: $bright-color;
      height: 3.4rem;
      border: none;
      appearance: none;
      border-radius: .4rem;
      font-family: $display-font;
      font-size: 1.4rem;
      font-weight: 600;
      align-self: flex-end;

      &:hover {
        background: $highlight-dark;
        cursor: pointer;
      }
    }
  }  

  @include responsive(desktop){
    .card{
      width: calc(20% - 1.6rem); 
      
      img{
        max-width: 100%;
      }
    }
  }

  @include responsive(tablet){
    .card{
      width: calc(25% - 1.6rem); 
      
      img{
        max-width: 100%;
      }
    }
  }

  @include responsive(mobile){
    .card{
      width: calc(50% - 1.6rem);
      
      img{
        max-width: 100%;
      }
    }
  }
}

.mainContent{
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  margin: 3.2rem 0;
}

.listColumn {
  width: 28rem;
  height: 100%;
  background: $bright-color;
  list-style: none;
  display: flex;
  flex-direction: column;
  padding: 1.6rem 3.2rem 0 1.6rem;
  border-radius: 0.8rem;

  > li {
    width: 100%;
    font-family: $display-font;
    font-size: 1.4rem;
    font-weight: 700;
    padding: 1.2rem 0;
    border-bottom: 0.1rem solid $mid-gray;

    &:last-child {
      border-bottom: none;
    }

    > button {
      background: transparent;
      border: none;
      width: auto;
      padding: 0;
      font-family: $display-font;
      font-size: 1.4rem;
      font-weight: 700;
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      align-items: center;
      cursor: pointer;
      transition: all 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);       

      &:hover {
        color: $highlight-color;
      }

      span {
        font-weight: 400;
        color: $highlight-dark;
        margin-left: 0.8rem;
        pointer-events: none;
      }
    }

    svg {
      width: 2.4rem;
      height: 2.4rem;
      margin-right: 0.8rem;
      pointer-events: none;
    }
    time{
      pointer-events: none;
    }

    > ul {
      width: 100%;
      list-style: none;
      padding: 0 0 0 1.6rem;
      display: none;

      button {
        background: transparent;
        border: none;
        font-family: $body-font;
        font-size: 1.4rem;
        color: $highlight-dark;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);
        text-align: left;

        &:hover {
          color: $highlight-color;
        }

        span{
          color: $base-color;
        }
      }
    }
  }

  .opened {
    display: block;
  }
}

.contentColumn {
  width: calc((100% - 31.6rem));
  display: flex;
  flex-wrap: wrap;
}

.listColumnMob{
  background: $bright-color;
  border-radius: .4rem;
  font-family: $display-font;
  font-weight: 600;
  color: $base-color;
  border: none;
  appearance: none;
  display: none;
  box-shadow: $shadow;
  padding: .8rem 1.6rem;

  svg{
    vertical-align: middle;
  }


  > ul {
    display: none;
    padding: 0;
    list-style: none;

    li{
      width: 100%;
      
      button {
        border: none;
        appearance: none;
        font-size: 1.6rem;
        color: $base-color;
        font-weight: 600;
        background: transparent;
        height: 3.4rem;

        &:hover {
          color: $highlight-color;
          cursor: pointer;
        }
      }
    }

    ul{
      padding: 0;
      list-style: none;
    }
    
  }
}

.listColumnMobOpen{
  display: table;
  text-align: left;
  > ul {
    display: block;   
  }
}

@include responsive(tablet){
  .listColumn{
    display: none;
  }

  .listColumnMob{
    width: 40rem; 
    display: block;   
  }  

  .contentColumn {
    margin-top: 3.2rem;
    width: 100%;
  }
}

@include responsive(mobile){
  .listColumn{
    display: none;
  }

  .listColumnMob{
    width: 100%;  
    display: block;  
  }  

  .contentColumn {
    margin-top: 3.2rem;
    width: 100%;
  }
}
