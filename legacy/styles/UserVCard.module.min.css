@import"https://fonts.googleapis.com/css2?family=Montserrat:wght@700&family=Open+Sans:ital,wght@0,400;0,700;1,400;1,700&display=swap";@media screen and (min-width: 0)and (max-width: 767px){.ax_section p{font-size:1.6rem;font-family:"Open Sans",sans-serif}.ax_container{width:90%;display:block;margin:0 auto}.ax_container p{font-family:"Open Sans",sans-serif;font-size:1.4rem;color:#000}}@media screen and (min-width: 768px)and (max-width: 1199px){.ax_section p{font-size:1.6rem;font-family:"Open Sans",sans-serif}.ax_container{width:74rem;display:block;margin:0 auto}}@media screen and (min-width: 1200px){.ax_section p{font-size:1.6rem;font-family:"Open Sans",sans-serif}.ax_container{width:114rem;display:block;margin:0 auto}}.ax_page_title{font-family:"Montserrat",sans-serif;font-size:2.4rem;margin:1.6rem 0 .8rem 0;color:#000;text-transform:capitalize}.ax_page_subtitle{font-family:"Montserrat",sans-serif;font-size:1.8rem;color:#2a7a94}.ax_form p a{font-weight:600;color:#2a7a94;text-decoration:underline}.ax_form .ax_form_heading{width:100%;margin-bottom:1.6rem}.ax_form .ax_form_heading p{font-size:1.6rem;font-family:"Open Sans",sans-serif;margin-top:0}.ax_form .ax_field{display:flex;flex-direction:column;position:relative}.ax_form .ax_field h1,.ax_form .ax_field h2,.ax_form .ax_field h3,.ax_form .ax_field h4,.ax_form .ax_field h5,.ax_form .ax_field h6{font-family:"Montserrat",sans-serif;margin-top:0;color:#2a7a94}.ax_form .ax_field label{color:#2a7a94;font-family:"Montserrat",sans-serif;font-size:1.4rem;font-weight:700;display:block;width:100%;margin-bottom:.4rem}.ax_form .ax_field label span{color:red;font-weight:800;font-size:1.6rem;line-height:1.6rem}.ax_form .ax_field label p{margin-top:0}.ax_form .ax_field label .counter{width:100%;display:block}.ax_form .ax_field label .counter span{font-size:1rem;font-weight:500;font-family:"Montserrat",sans-serif}.ax_form .ax_field textarea,.ax_form .ax_field input[type=text],.ax_form .ax_field input[type=email],.ax_form .ax_field input[type=password],.ax_form .ax_field input[type=number],.ax_form .ax_field input[type=tel],.ax_form .ax_field input[type=date],.ax_form .ax_field select{width:100%;border-radius:.4rem;border:.1rem solid #d7dfe9;background:#f2f5f9;padding:0 4rem 0 1.6rem;color:#000;font-family:"Montserrat",sans-serif;font-size:1.4rem;display:inline-block;margin-bottom:1.6rem}.ax_form .ax_field input[type=text],.ax_form .ax_field input[type=email],.ax_form .ax_field input[type=password],.ax_form .ax_field input[type=number],.ax_form .ax_field input[type=tel],.ax_form .ax_field input[type=date],.ax_form .ax_field input[type=date],.ax_form .ax_field input[type=time],.ax_form .ax_field input[type=number],.ax_form .ax_field input[type=url],.ax_form .ax_field select{height:4rem}.ax_form .ax_field input[type=text]::-moz-placeholder, .ax_form .ax_field input[type=email]::-moz-placeholder, .ax_form .ax_field input[type=password]::-moz-placeholder, .ax_form .ax_field input[type=number]::-moz-placeholder, .ax_form .ax_field input[type=tel]::-moz-placeholder, .ax_form .ax_field input[type=date]::-moz-placeholder, .ax_form .ax_field input[type=date]::-moz-placeholder, .ax_form .ax_field input[type=time]::-moz-placeholder, .ax_form .ax_field input[type=number]::-moz-placeholder, .ax_form .ax_field input[type=url]::-moz-placeholder, .ax_form .ax_field select::-moz-placeholder{font-weight:400;color:#d7dfe9}.ax_form .ax_field input[type=text]::placeholder,.ax_form .ax_field input[type=email]::placeholder,.ax_form .ax_field input[type=password]::placeholder,.ax_form .ax_field input[type=number]::placeholder,.ax_form .ax_field input[type=tel]::placeholder,.ax_form .ax_field input[type=date]::placeholder,.ax_form .ax_field input[type=date]::placeholder,.ax_form .ax_field input[type=time]::placeholder,.ax_form .ax_field input[type=number]::placeholder,.ax_form .ax_field input[type=url]::placeholder,.ax_form .ax_field select::placeholder{font-weight:400;color:#d7dfe9}.ax_form .ax_field textarea{padding:1.6rem;min-height:16rem}.ax_form .ax_field button.see{position:absolute;top:2.2rem;right:0;width:4rem;height:4rem;border:none;background:rgba(0,0,0,0)}.ax_form .ax_field button.see svg{color:#414141}.ax_form .ax_field small{font-family:"Montserrat",sans-serif;font-size:1.4rem}.ax_form .ax_field input[type=checkbox]{margin-bottom:1.6rem;position:relative;-webkit-appearance:none;-moz-appearance:none;appearance:none}.ax_form .ax_field input[type=submit]{height:4rem;border-radius:.4rem;background:#3caba7;color:#fff;font-family:"Montserrat",sans-serif;font-size:1.6rem;font-weight:600;border:none}.ax_form input[type=checkbox]{-webkit-appearance:none;-moz-appearance:none;appearance:none;font-size:1.6rem;line-height:1.6rem;text-align:center;width:1.6rem;height:1.6rem;border-radius:.2rem;border:.2rem solid #d7dfe9;background:#f2f5f9;display:inline-block;position:relative;vertical-align:middle;margin-bottom:.4rem !important}.ax_form input[type=checkbox]:checked:before{content:"✓";font-size:1.6rem;line-height:1.6rem;text-align:center;width:1.2rem;height:1.2rem;border-radius:.2rem;background:rgba(0,0,0,0);border:none;display:block;position:absolute;left:0;top:0;z-index:50}.ax_field_with_icon{display:flex;flex-wrap:wrap;flex-direction:row;justify-content:flex-start}.ax_field_with_icon .iconInput{width:100%;display:block;position:relative}.ax_field_with_icon .iconInput>svg,.ax_field_with_icon .iconInput>img{position:absolute;top:.6rem;left:.6rem;z-index:10}.ax_field_with_icon h1,.ax_field_with_icon h2,.ax_field_with_icon h3,.ax_field_with_icon h4,.ax_field_with_icon h5,.ax_field_with_icon h6{font-family:"Montserrat",sans-serif;margin-top:0;color:#2a7a94}.ax_field_with_icon label{color:#2a7a94;font-family:"Montserrat",sans-serif;font-size:1.4rem;font-weight:700;display:block;width:100%;margin-bottom:.4rem}.ax_field_with_icon label span{color:red;font-weight:800;font-size:1.6rem}.ax_field_with_icon label p{margin-top:0}.ax_field_with_icon textarea,.ax_field_with_icon input[type=text],.ax_field_with_icon input[type=email],.ax_field_with_icon input[type=password],.ax_field_with_icon input[type=number],.ax_field_with_icon input[type=tel],.ax_field_with_icon input[type=date],.ax_field_with_icon select{width:100%;border-radius:.4rem;border:.1rem solid #d7dfe9;background:#f2f5f9;padding:0 4rem 0 3.8rem;color:#000;font-family:"Montserrat",sans-serif;font-size:1.4rem;display:inline-block;margin-bottom:1.6rem}.ax_field_with_icon input[type=text],.ax_field_with_icon input[type=email],.ax_field_with_icon input[type=password],.ax_field_with_icon input[type=number],.ax_field_with_icon input[type=tel],.ax_field_with_icon input[type=date],.ax_field_with_icon input[type=date],.ax_field_with_icon input[type=time],.ax_field_with_icon input[type=number],.ax_field_with_icon input[type=url],.ax_field_with_icon select{height:4rem}.ax_field_with_icon input[type=text]::-moz-placeholder, .ax_field_with_icon input[type=email]::-moz-placeholder, .ax_field_with_icon input[type=password]::-moz-placeholder, .ax_field_with_icon input[type=number]::-moz-placeholder, .ax_field_with_icon input[type=tel]::-moz-placeholder, .ax_field_with_icon input[type=date]::-moz-placeholder, .ax_field_with_icon input[type=date]::-moz-placeholder, .ax_field_with_icon input[type=time]::-moz-placeholder, .ax_field_with_icon input[type=number]::-moz-placeholder, .ax_field_with_icon input[type=url]::-moz-placeholder, .ax_field_with_icon select::-moz-placeholder{font-weight:400;color:#d7dfe9}.ax_field_with_icon input[type=text]::placeholder,.ax_field_with_icon input[type=email]::placeholder,.ax_field_with_icon input[type=password]::placeholder,.ax_field_with_icon input[type=number]::placeholder,.ax_field_with_icon input[type=tel]::placeholder,.ax_field_with_icon input[type=date]::placeholder,.ax_field_with_icon input[type=date]::placeholder,.ax_field_with_icon input[type=time]::placeholder,.ax_field_with_icon input[type=number]::placeholder,.ax_field_with_icon input[type=url]::placeholder,.ax_field_with_icon select::placeholder{font-weight:400;color:#d7dfe9}.ax_field_with_icon textarea{padding:1.6rem;min-height:16rem}.ax_field_with_icon button.see{position:absolute;top:2.2rem;right:0;width:4rem;height:4rem;border:none;background:rgba(0,0,0,0)}.ax_field_with_icon button.see svg{color:#414141}.ax_field_with_icon small{font-family:"Montserrat",sans-serif;font-size:1.4rem}.ax_field_with_icon input[type=checkbox]{margin-bottom:1.6rem}.ax_field_with_icon input[type=submit]{height:4rem;border-radius:.4rem;background:#3caba7;color:#fff;font-family:"Montserrat",sans-serif;font-size:1.6rem;font-weight:600;border:none}.inputDanger{border:.2rem solid #de1600 !important}.ax_datepicker{width:100%;height:4rem;border-radius:.4rem;border:.1rem solid #d7dfe9;background:#f2f5f9;padding:0 4rem 0 1.6rem;color:#000;font-family:"Montserrat",sans-serif;font-size:1.4rem;display:inline-block;margin-bottom:1.6rem}.ax_datepicker::-moz-placeholder{font-weight:400;color:#d7dfe9}.ax_datepicker::placeholder{font-weight:400;color:#d7dfe9}.ax_datepicker .datepickerToggle{display:inline-block;position:relative;width:18px;height:19px}.ax_datepicker .datepickerToggleButton{position:absolute;right:.8rem;top:.6rem;width:100%;height:100%;background:blue}.ax_datepicker .datepickerInput{position:absolute;left:0;top:0;width:100%;height:100%;opacity:0;cursor:pointer;box-sizing:border-box}.ax_datepicker .datepickerInput::-webkit-calendar-picker-indicator{position:absolute;left:0;top:0;width:100%;height:100%;margin:0;padding:0;cursor:pointer}.ax_card_list{width:100%;display:flex;flex-direction:row;justify-content:flex-start;flex-wrap:wrap}.boxedLoading{width:100%;height:100%;background:rgba(170,247,166,.8);display:flex;flex-direction:row;justify-content:center;align-items:center;position:absolute;left:0;top:0;z-index:500}.primary{color:#3caba7}.base{color:#000}.purple{color:#7020bc}.orange{color:#e2591b}.green{color:#8eb440}.yellow{color:#e6b800}.blue{color:#2d4bd2}.teal{color:#00a8a8}.img_responsive{width:100%;max-width:100%;height:auto}.textCenter{text-align:center}.textRight{text-align:right}.centerContent{width:100%;justify-content:center !important;align-items:center !important}.validation{padding:1.6rem;background:rgba(255,0,0,.1);border:.1rem solid rgba(255,0,0,.3);border-radius:.8rem;margin-bottom:3.2rem}.validation h3{font-family:"Montserrat",sans-serif;font-size:1.6rem;margin:0 0 1.6rem 0;color:#a13}.validation ul{list-style:none;padding:0;margin:0}.validation ul li{padding-left:.8rem;position:relative;color:#a13;font-family:"Montserrat",sans-serif;font-size:1.2rem;line-height:1.8rem;font-weight:600}.validation ul li:before{content:"";width:.4rem;height:.4rem;display:block;border-radius:50%;background:#a13;position:absolute;top:.6rem;left:0}.dragzone{width:100%;height:10rem;height:auto;display:block;position:relative;z-index:1}.dragzone span{width:100%;line-height:4.8rem;font-family:"Montserrat",sans-serif;color:#666;font-weight:700;pointer-events:none;position:absolute;top:28%;text-align:center;z-index:1;transition:all .2s ease-out}.dragzone .dragarea{width:100%;height:10rem;display:flex;justify-content:center;align-items:center;position:relative;background:#f2f5f9;border-radius:.8rem;border:.2rem dashed #d7dfe9;z-index:10;transition:all .2s ease-out;cursor:grab}.dragzone .dragarea:hover{background:#a6b3c4;cursor:grab}.dragzone .dragarea:hover span{color:#fff}.contentBox{padding:3.2rem;background:#fff;border-radius:.8rem;margin-bottom:3.2rem;width:100%;display:flex;flex-wrap:wrap}.contentBox h2,.contentBox h3,.contentBox h4,.contentBox h5,.contentBox h6{width:100%;display:block;font-family:"Montserrat",sans-serif;color:#000;margin:0 0 1.6rem 0}.contentBox h2{font-size:2.4rem;line-height:2.8rem}.contentBox h3{font-size:2.1rem;line-height:2.4rem}.contentBox h4{font-size:1.8rem;line-height:2.1rem}.contentBox h5{font-size:1.6rem;line-height:2.1rem}.contentBox h6{font-size:1.4rem;line-height:1.8rem}.contentBox p{font-family:"Open Sans",sans-serif;font-size:1.6rem;line-height:2.1rem;color:#000;margin:0 0 1.6rem 0}.contentBox p img{max-width:100%;height:auto;display:inline-block}.contentBox p a{color:#2a7a94;text-decoration:underline}.contentBox p a:hover{color:#3caba7}.contentBox p a img{max-width:100%;height:auto;display:inline-block}.contentBox img{max-width:100%;height:auto;display:inline-block}.contentBox ul{list-style:none;padding:0;margin-left:3.2rem;margin-bottom:3.2rem}.contentBox ul li{position:relative;padding-left:.8rem;display:block;font-family:"Open Sans",sans-serif;font-size:1.6rem;line-height:2.8rem}.contentBox ul li:before{content:"";width:.8rem;height:.8rem;display:block;border-radius:50%;position:absolute;top:1rem;left:-0.8rem;z-index:1;background-color:#aae2e0}.contentBox ol{padding:0;margin-left:3.2rem;margin-bottom:3.2rem}.contentBox ol li{position:relative;margin-left:.8rem;font-family:"Open Sans",sans-serif;font-size:1.6rem;line-height:2.8rem}.contentBox ol li::marker{color:#2a7a94}.contentBox pre{width:100%;background:#f2f5f9;border-radius:.8rem;overflow-x:scroll;padding:1.6rem;margin-bottom:3.2rem}.contentBox pre code{width:100%;display:table;font-family:monospace;font-size:1.4rem;line-height:1.8rem}.contentBox blockquote{border-left:.4rem solid #aae2e0;padding-left:1.6rem;font-family:"Montserrat",sans-serif;font-weight:600;margin:3.2rem 0 3.2rem 4rem}.contentBox blockquote p{color:#666}.contentBox blockquote p a{color:#2a7a94;text-decoration:underline}.sepparator{width:100%;height:.2rem;background:#d7dfe9;border-radius:.1rem;display:block}.forceParagraph{font-size:1.4rem !important;font-weight:400 !important;font-family:"Open Sans",sans-serif !important}.ax_tip{font-family:"Montserrat",sans-serif;border-left:.4rem solid #3caba7;background:#f2f5f9;color:#000;border-radius:0 .8rem .8rem 0;padding:.8rem}.ax_tip span{color:#3caba7}.ax_tip_error{font-family:"Montserrat",sans-serif;border-left:.4rem solid red;background:#f2f5f9;color:#540000;border-radius:0 .8rem .8rem 0;padding:.8rem}.ax_tip_error span{color:#3caba7}.alertBox{margin:auto;width:40rem;height:auto;border-radius:.8rem;background:#fff;grid-template-columns:37rem 3rem;grid-template-areas:"title close" "content content" "footer footer";position:relative;top:-15rem;display:grid}.alertBox h3{grid-area:title;width:calc(100% - 2rem);font-family:"Montserrat",sans-serif;font-size:1.6rem;margin:0;padding:1.6rem 0 0 1.6rem}.alertBox>button{width:2rem;height:2rem;grid-area:button;border:none;color:#3caba7;background:#d7dfe9;border-radius:50%;padding:0;cursor:pointer;grid-area:close;margin:.8rem 0 0 0;transition:all .2s ease-out}.alertBox>button svg{position:relative;top:.2rem;fill:#000}.alertBox>button:hover{background:red}.alertBox>button:hover svg{fill:#fff}.alertBox .content{grid-area:content;width:100%;padding:0 1.6rem}.alertBox .content p{font-family:"Open Sans",sans-serif;font-size:1.4rem;color:#000}.alertBox footer{grid-area:footer;display:flex;justify-content:space-between;align-items:center;height:5.6rem;padding:.8rem 1.6rem 1.6rem 1.6rem}.alertBox footer button{width:auto;background:#3caba7;color:#fff;height:3.4rem;margin-bottom:.8rem;font-family:"Montserrat",sans-serif;font-weight:600;padding:.8rem 2rem;border:none;border-radius:.4rem;cursor:pointer;transition:all .2s ease-out}.alertBox footer button:hover{background:#3ba9a5}.cardUser{width:calc(25% - 3.2rem);height:12rem;margin:0 1.6rem 3.2rem 0;transition:all .2s ease-out}.cardUser:hover:before{background:#000}.cardUser .cardContainer{width:100%;height:100%;padding:1rem;display:flex;flex-wrap:wrap;justify-content:flex-start;align-items:flex-start;text-align:center;background:#fff;border-radius:6rem;transition:all .2s ease-out;position:relative}.cardUser .cardContainer .overlay{display:none;justify-content:center;align-items:center;background:#3caba7;width:100%;height:100%;position:absolute;top:0;left:0;transition:all .2s ease-out;border-radius:6rem;-webkit-appearance:none;-moz-appearance:none;appearance:none;border:none}.cardUser .cardContainer .overlay p{color:#fff;font-size:1.4rem;font-family:"Montserrat",sans-serif;font-weight:600}.cardUser .cardContainer:hover .overlay{display:flex;cursor:pointer}.cardUser .cardContainer .photo{width:10rem;height:10rem;border-radius:50%;overflow:hidden;margin:0 1.6rem 0 0;display:block;background-size:cover;background-repeat:no-repeat;background-position:top center;background-color:#d7dfe9}.cardUser .cardContainer .cardBody{width:calc(100% - 11.6rem);max-width:calc(100% - 11.6rem);display:flex;flex-direction:column;justify-content:center;align-items:flex-start;text-align:left;height:100%}.cardUser .cardContainer .cardBody h3{font-family:"Montserrat",sans-serif;font-size:1.6rem;font-weight:600;color:#000;margin:0 0 1.2rem 0;padding-right:1.6rem}.cardUser .cardContainer .cardBody p{font-family:"Montserrat",sans-serif;font-size:1.4rem;line-height:2.1rem;font-weight:400;color:#000;margin:0;word-break:break-all}.cardUser .cardContainer .cardBody p strong{font-weight:600}@media screen and (min-width: 1200px){.level1{width:30rem}.level2{width:24rem;height:9.6rem;margin:1.6rem 1.6rem 3.2rem 0}.level3,.level4{width:18.5rem;height:7.6rem;margin:1.6rem 1.6rem 3.2rem 0}.level5{width:calc(33% - 3.2rem)}}@media screen and (min-width: 768px)and (max-width: 1199px){.cardUser{width:calc(50% - 3.2rem)}}@media screen and (min-width: 0)and (max-width: 767px){.cardUser{width:calc(100% - 3.2rem)}}.level1,.level2,.level3,.level4,.level5{position:relative}.level1 .cardContainer,.level2 .cardContainer,.level3 .cardContainer,.level4 .cardContainer,.level5 .cardContainer{background:#212121;position:relative;z-index:2}.level1 .cardContainer h3,.level1 .cardContainer h4,.level2 .cardContainer h3,.level2 .cardContainer h4,.level3 .cardContainer h3,.level3 .cardContainer h4,.level4 .cardContainer h3,.level4 .cardContainer h4,.level5 .cardContainer h3,.level5 .cardContainer h4{margin:0 0 .4rem 0;font-family:"Montserrat",sans-serif}.level1 .cardContainer h3,.level2 .cardContainer h3,.level3 .cardContainer h3,.level4 .cardContainer h3,.level5 .cardContainer h3{color:#fff !important}.level1 .cardContainer h4,.level2 .cardContainer h4,.level3 .cardContainer h4,.level4 .cardContainer h4,.level5 .cardContainer h4{color:#3caba7;font-size:1.1rem;padding-right:1.6rem}.level1 .cardContainer .photo,.level2 .cardContainer .photo,.level3 .cardContainer .photo,.level4 .cardContainer .photo,.level5 .cardContainer .photo{border:.6rem solid #323232}.level1 .cardContainer:before,.level1 .cardContainer:after,.level2 .cardContainer:before,.level2 .cardContainer:after,.level3 .cardContainer:before,.level3 .cardContainer:after,.level4 .cardContainer:before,.level4 .cardContainer:after,.level5 .cardContainer:before,.level5 .cardContainer:after{content:"";width:4rem;height:1rem;background:#fff;position:absolute;z-index:1}.level1 .cardContainer:before,.level2 .cardContainer:before,.level3 .cardContainer:before,.level4 .cardContainer:before,.level5 .cardContainer:before{top:-1rem;left:7rem}.level1 .cardContainer:after,.level2 .cardContainer:after,.level3 .cardContainer:after,.level4 .cardContainer:after,.level5 .cardContainer:after{bottom:-1rem;left:7rem}.level1:before,.level2:before,.level3:before,.level4:before,.level5:before{content:"";width:14rem;height:14rem;background:linear-gradient(135.91deg, #6d9db8 2.47%, #415a71 98.32%);position:absolute;top:-1rem;left:-1rem;z-index:1;border-radius:7rem}.level2 .cardContainer,.level3 .cardContainer,.level4 .cardContainer,.level5 .cardContainer{background:#f2f5f9}.level2 .cardContainer h3,.level3 .cardContainer h3,.level4 .cardContainer h3,.level5 .cardContainer h3{color:#000 !important}.level2 .cardContainer h4,.level3 .cardContainer h4,.level4 .cardContainer h4,.level5 .cardContainer h4{color:#2a7a94}.level2 .cardContainer .photo,.level3 .cardContainer .photo,.level4 .cardContainer .photo,.level5 .cardContainer .photo{border:.6rem solid #d7dfe9}.level2:before{content:"";background:linear-gradient(135.91deg, #cefffc 2.47%, #009176 98.32%)}.level2 .cardContainer{width:100%;height:100%;padding:.8rem}.level2 .cardContainer .photo{width:8rem;height:8rem;margin:0 1.6rem 0 0}.level2 .cardContainer .cardBody{width:calc(100% - 11.6rem);max-width:calc(100% - 11.6rem);margin-top:0}.level2 .cardContainer .cardBody h3{font-size:1.3rem;margin-bottom:.4rem;margin-top:0}.level2 .cardContainer:before{width:6rem;top:-1rem;left:5rem !important}.level2 .cardContainer:after{width:6rem;bottom:-1rem;left:5rem !important}.level2:before{height:11rem;top:-0.7rem;left:-0.7rem !important}.level3 .cardContainer .cardBody{width:calc(100% - 11.6rem);max-width:calc(100% - 11.6rem)}.level3 .cardContainer,.level4 .cardContainer{width:100%;height:100%;padding:.8rem}.level3 .cardContainer .photo,.level4 .cardContainer .photo{width:6rem;height:6rem;margin:0 .8rem 0 0}.level3 .cardContainer .cardBody,.level4 .cardContainer .cardBody{width:calc(100% - 8rem);max-width:calc(100% - 8rem);margin-top:0}.level3 .cardContainer .cardBody h3,.level4 .cardContainer .cardBody h3{font-size:1rem;margin-bottom:.4rem;margin-top:0}.level3 .cardContainer .cardBody h4,.level4 .cardContainer .cardBody h4{font-size:.9rem}.level3 .cardContainer:before,.level4 .cardContainer:before{width:3rem;top:-1rem;left:4rem !important}.level3 .cardContainer:after,.level4 .cardContainer:after{width:3rem;bottom:-1rem;left:4rem !important}.level3:before,.level4:before{width:9rem;height:9rem;top:-0.7rem;left:-0.7rem !important}.level5{width:calc(15% - 3.2rem);height:8rem;margin:0 1.2rem 2.4rem 0}.level5 .cardContainer{width:100%;height:100%;padding:.8rem}.level5 .cardContainer .photo{width:6rem;height:6rem;margin:0 1.2rem 0 0}.level5 .cardContainer .cardBody{width:calc(100% - 8.6rem);max-width:calc(100% - 8.6rem);margin-top:0}.level5 .cardContainer .cardBody h3{font-size:.9rem;margin-bottom:.4rem;margin-top:0}.level5 .cardContainer .cardBody h4{font-size:.8rem}.level5 .cardContainer:before{width:3rem;top:-1rem;left:4rem !important}.level5 .cardContainer:after{width:3rem;bottom:-1rem;left:4rem !important}.level5:before{width:9rem;height:9rem;top:-0.5rem;left:-0.5rem !important}.level3:before{content:"";background:linear-gradient(135.91deg, #a8eefd 2.47%, #023986 98.32%)}.level4:before{content:"";background:linear-gradient(135.88deg, #d1cbf8 1.49%, #230086 100%)}.level5:before{content:"";background:linear-gradient(135.91deg, #ffefae 2.47%, #dc8400 98.32%)}/*# sourceMappingURL=UserVCard.module.min.css.map */