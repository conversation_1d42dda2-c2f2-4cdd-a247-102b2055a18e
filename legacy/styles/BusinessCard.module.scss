@import 'theme';
@import 'mixins';

.frontNull, .backNull{
  border-radius: .8rem;
  display: block;
  margin: 3.2rem 0;
  box-shadow: .2rem .2rem .8rem rgba(0,0,0,.15);
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  border: .1rem solid $light-gray;
  text-align: center;

  h3{
    font-size: 1.4rem;
    color: $gray !important;
  }
}

.businessCardFront {
  border-radius: .8rem;
  display: block;
  margin: 3.2rem 0;
  box-shadow: .2rem .2rem .8rem rgba(0,0,0,.15);
  position: relative;
  z-index: 10;

  .heading{
    position: absolute;
    h2{
      color: #000;  
      margin: 0;          
    }
    h3{
      color: $highlight-color;    
    }
  }

  .contact{
    position: absolute;
    p{
      color: #fff;
    }
  }

  .social{
    position: absolute;
    p{
      color: #fff;
      width: 100%;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      justify-content: flex-start;

      span{
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        justify-content: flex-start;

        img{
          display: inline-block;
        }
      }
    }
  }
}

.businessCardBack{
  border-radius: .8rem;
  display: block;
  margin: 3.2rem 0;
  box-shadow: .2rem .2rem .8rem rgba(0,0,0,.15);
  position: relative;
}

.front1{
  background-image:  url('/images/bg-front1.svg');
  background-repeat: no-repeat;
  background-size: contain;
}

.front2{
  position: relative;
}

.back1{
  background-image:  url('/images/bg-back1.svg');
  background-repeat: no-repeat;
  background-size: contain;
}

.back2{
  background-image:  url('/images/bg-back2.svg');
  background-repeat: no-repeat;
  background-size: contain;
}

@include responsive(desktop){
  .frontNull, .backNull{
    width: 47.3rem;
    height: 27rem; 
  }

  .front1 {
    width: 47.3rem;
    height: 27rem;   

    .heading{
      top: 2rem;
      left: 5.4rem;

      h2{
        font-size: 2.1rem;     
        span{
          font-size: 1.6rem;
        }       
      }
      h3{
        font-size: 1.2rem;     
        font-weight: 500;
        margin: 0;
      }
    }

    .contact{
      left: 2.4rem;
      top: 14.6rem;

      p{
        color: #fff;
        margin: 0;
        font-size: 1.4rem;

        img{
          height: 1rem;
          width: auto;
          display: inline-block;
        }
      }
    }

    .social{
      left: 2.4rem;
      bottom: 0;

      p{
        color: #fff;
        font-size: 1.1rem;

        span{
          margin-right: .8rem;
          img{
            height: 1.2rem;
            width: auto;
            margin-right: .1rem;
          }
        }
      }
    }
  } 

  .front2{
    width: 47.3rem;
    height: 27rem; 

    .heading{
      top: 2rem;
      left: 5.4rem;
      z-index: 500;

      h2{
        font-size: 2.1rem;     
        span{
          font-size: 1.6rem;
        }       
      }
      h3{
        font-size: 1.2rem;     
        font-weight: 500;
        margin: 0;
      }
    }

    .frontDesign{
      width: 47.3rem;
      height: 27rem; 
      position: absolute;
      bottom: 0;
      left: 0;
      z-index: 100;

      img{
        width: 100%;
        max-width: 100%;
        height: auto;
      }
    }

    .photo {
      width: 14rem;
      height: auto;
      display: block;
      position: absolute;
      right: 0rem;
      bottom: 4rem;
      z-index: 10;

      img{
        width: 100%;
        max-width: 100%;
        height: auto;
      }
    }

    .contact{
      left: 2.4rem;
      top: 14.6rem;
      z-index: 500;

      p{
        color: #fff;
        margin: 0;
        font-size: 1.4rem;

        img{
          height: 1rem;
          width: auto;
          display: inline-block;
        }
      }
    }

    .social{
      left: 2.4rem;
      bottom: 0;
      z-index: 500;

      p{
        color: #fff;
        font-size: 1.1rem;

        span{
          margin-right: .8rem;
          img{
            height: 1.2rem;
            width: auto;
            margin-right: .1rem;
          }
        }
      }
    } 
  }

  .back1{
    width: 47.3rem;
    height: 27rem; 

    p{
      width: 100%;
      display: block;
      text-align: center;
      position: absolute;
      bottom: .8rem;
      color: #fff;
    }
  }
  
  .back2{
    width: 47.3rem;
    height: 27rem; 

    p{
      width: 100%;
      display: block;
      text-align: center;
      position: absolute;
      bottom: .8rem;
      color: #fff;
    }
  }
}

@include responsive(tablet){
  .front1, .back1, .frontNull, .backNull {
    width: 48rem;
    height: 27rem;   
  }  
  
  .front1 {
    width: 47.3rem;
    height: 27rem;   

    .heading{
      top: 2rem;
      left: 5.4rem;

      h2{
        font-size: 2.1rem;     
        span{
          font-size: 1.6rem;
        }       
      }
      h3{
        font-size: 1.2rem;     
        font-weight: 500;
        margin: 0;
      }
    }

    .contact{
      left: 2.4rem;
      top: 14.6rem;

      p{
        color: #fff;
        margin: 0;
        font-size: 1.4rem;

        img{
          height: 1rem;
          width: auto;
          display: inline-block;
        }
      }
    }

    .social{
      left: 2.4rem;
      bottom: 0;

      p{
        color: #fff;
        font-size: 1.1rem;

        span{
          margin-right: .8rem;
          img{
            height: 1.2rem;
            width: auto;
            margin-right: .1rem;
          }
        }
      }
    }
  } 

  .front2{
    width: 47.3rem;
    height: 27rem; 

    .heading{
      top: 2rem;
      left: 5.4rem;
      z-index: 500;

      h2{
        font-size: 2.1rem;     
        span{
          font-size: 1.6rem;
        }       
      }
      h3{
        font-size: 1.2rem;     
        font-weight: 500;
        margin: 0;
      }
    }

    .frontDesign{
      width: 47.3rem;
      height: 27rem; 
      position: absolute;
      bottom: 0;
      left: 0;
      z-index: 100;

      img{
        width: 100%;
        max-width: 100%;
        height: auto;
      }
    }

    .photo {
      width: 14rem;
      height: auto;
      display: block;
      position: absolute;
      right: 0rem;
      bottom: 4rem;
      z-index: 10;

      img{
        width: 100%;
        max-width: 100%;
        height: auto;
      }
    }

    .contact{
      left: 2.4rem;
      top: 14.6rem;
      z-index: 500;

      p{
        color: #fff;
        margin: 0;
        font-size: 1.4rem;

        img{
          height: 1rem;
          width: auto;
          display: inline-block;
        }
      }
    }

    .social{
      left: 2.4rem;
      bottom: 0;
      z-index: 500;

      p{
        color: #fff;
        font-size: 1.1rem;

        span{
          margin-right: .8rem;
          img{
            height: 1.2rem;
            width: auto;
            margin-right: .1rem;
          }
        }
      }
    } 
  }

  .back1{
    width: 47.3rem;
    height: 27rem; 

    p{
      width: 100%;
      display: block;
      text-align: center;
      position: absolute;
      bottom: .8rem;
      color: #fff;
    }
  }
  
  .back2{
    width: 47.3rem;
    height: 27rem; 

    p{
      width: 100%;
      display: block;
      text-align: center;
      position: absolute;
      bottom: .8rem;
      color: #fff;
    }
  }
}

@include responsive(mobile){
  .front1, .back1, .frontNull, .backNull {
    width: 31rem;
    height: 17.7rem;   
  }  

  .front1 {
    width: 31rem;
    height: 17.7rem;       

    .heading{
      top: 2rem;
      left: 4rem;

      h2{
        font-size: 1.4rem;     
        span{
          font-size: 1.1rem;
        }       
      }
      h3{
        font-size: .9rem;     
        font-weight: 500;
        margin: 0;
      }
    }

    .contact{
      left: 1.4rem;
      top: 9.2rem;

      p{
        color: #fff;
        margin: 0;
        font-size: .8rem;
        line-height: 1.4rem;

        img{
          height: .5rem;
          width: auto;
          display: inline-block;
        }
      }
    }

    .social{
      left: 1.4rem;
      bottom: 0;

      p{
        color: #fff;
        font-size: .7rem;

        span{
          margin-right: .4rem;
          img{
            height: .8rem;
            width: auto;
            margin-right: .1rem;
          }
        }
      }
    }
  } 

  .front2{
    width: 31rem;
    height: 17.7rem;    

    .heading{
      top: 2rem;
      left: 4rem;

      h2{
        font-size: 1.4rem;     
        span{
          font-size: 1.1rem;
        }       
      }
      h3{
        font-size: .9rem;     
        font-weight: 500;
        margin: 0;
      }
    }

    .frontDesign{
      width: 31rem;
      height: 17.7rem;    
      position: absolute;
      bottom: 0;
      left: 0;
      z-index: 100;

      img{
        width: 100%;
        max-width: 100%;
        height: auto;
      }
    }

    .photo {
      width: 10rem;
      height: auto;
      display: block;
      position: absolute;
      right: 0rem;
      bottom: 0rem;
      z-index: 10;

      img{
        width: 100%;
        max-width: 100%;
        height: auto;
      }
    }

    .contact{
      left: 1.4rem;
      top: 9.2rem;
      z-index: 500;

      p{
        color: #fff;
        margin: 0;
        font-size: .8rem;
        line-height: 1.4rem;

        img{
          height: .5rem;
          width: auto;
          display: inline-block;
        }
      }
    }

    .social{
      left: 1.4rem;
      bottom: 0;
      z-index: 500;

      p{
        color: #fff;
        font-size: .7rem;

        span{
          margin-right: .4rem;
          img{
            height: .8rem;
            width: auto;
            margin-right: .1rem;
          }
        }
      }
    }
  }

  .back1{
    width: 31rem;
    height: 17.7rem;     

    p{
      width: 100%;
      display: block;
      text-align: center;
      position: absolute;
      bottom: .8rem;
      color: #fff;
      font-size: 1rem;
    }
  }
  
  .back2{
    width: 31rem;
    height: 17.7rem;    

    p{
      width: 100%;
      display: block;
      text-align: center;
      position: absolute;
      bottom: .4rem;
      color: #fff;
      font-size: 1rem;
    }
  }
}