@import 'theme.scss';
@import 'mixins.scss';

.percentBarHeading {
  font-size: 1.2rem;
  font-family: $display-font;
  font-weight: 700;
  margin: 0.6rem 0 0.2rem 0;
}

.percentBarContainer {
  width: 100%;
  display: block;
}

.percentBar {
  width: 100%;
  height: 1.4rem;
  overflow: hidden;
  border-radius: 0.8rem;
  background: $mid-gray;
  display: block;

  .innerBar {
    background-color: $highlight-color;
    background-image: none;
    height: 1.4rem;
    display: block;
    position: relative;
    z-index: 1;

    span {
      color: #fff;
      font-family: $display-font;
      font-size: 0.8rem;
      font-weight: 700;
      line-height: 0.1rem;
      margin: 0;
      position: absolute;
      top: 0.7rem;
      right: 0.3rem;
      z-index: 2;
    }
  }

  .innerBarLoading {
    width: 200%;
    height: 1.4rem;
    overflow: hidden;
    display: block;
    background-color: transparent;
    background-image: url('../public/images/loading-bars.svg');
    background-repeat: repeat-x;
    animation: infinitHorizontalLoop 40s linear infinite;
  }
}
