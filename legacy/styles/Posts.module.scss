@import 'theme';
@import 'mixins';

.article {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  border-radius: 0.8rem;
  background: $bright-color;
  padding: 3.2rem;
  margin: 3.2rem 0;

  .heading {
    display: block;
    width: 100%;
    border-bottom: 0.1rem solid $mid-gray;
    font-family: $display-font;

    h2 {
      font-size: 2.8rem;
      line-height: 3.6rem;
      margin-top: 0;
      margin-bottom: 0.8rem;
    }

    h3 {
      width: 100%;
      display: block;
      font-family: $display-font;
      font-size: 1.4rem;
      margin-bottom: 1.6rem;
      color: $gray;
      font-weight: 400;
      padding-bottom: 0.8rem;

      span {
        color: $gray;
        font-weight: 600;
      }
    }
  }

  .contentBody {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    border-radius: 0 0.8rem 0.8rem 0;
    padding: 0;

    h2,
    h3,
    h4,
    h5,
    h6 {
      width: 100%;
      display: block;
      font-family: $display-font;
      color: $highlight-dark;
      margin: 0 0 1.6rem 0;
    }

    h2 {
      font-size: 2.4rem;
      line-height: 2.8rem;
    }

    h3 {
      font-size: 2.1rem;
      line-height: 2.4rem;
    }

    h4 {
      font-size: 1.8rem;
      line-height: 2.1rem;
    }

    h5 {
      font-size: 1.6rem;
      line-height: 2.1rem;
    }

    h6 {
      font-size: 1.4rem;
      line-height: 1.8rem;
    }

    p {
      font-family: $body-font;
      font-size: 1.6rem;
      line-height: 2.1rem;
      color: $base-color;
      margin: 0 0 1.6rem 0;

      img {
        max-width: 100%;
        height: auto;
        display: inline-block;
      }

      a {
        color: $highlight-dark;
        text-decoration: underline;

        &:hover {
          color: $highlight-color;
        }

        img {
          max-width: 100%;
          height: auto;
          display: inline-block;
        }
      }
    }

    img {
      max-width: 100%;
      height: auto;
      display: inline-block;
    }

    ul {
      list-style: none;
      padding: 0;
      margin-left: 3.2rem;
      margin-bottom: 3.2rem;

      li {
        position: relative;
        padding-left: 0.8rem;
        display: block;
        font-family: $body-font;
        font-size: 1.6rem;
        line-height: 2.8rem;

        &:before {
          content: '';
          width: 0.8rem;
          height: 0.8rem;
          display: block;
          border-radius: 50%;
          position: absolute;
          top: 1rem;
          left: -0.8rem;
          z-index: 1;
          background-color: $light-green;
        }
      }
    }

    ol {
      padding: 0;
      margin-left: 3.2rem;
      margin-bottom: 3.2rem;

      li {
        position: relative;
        margin-left: 0.8rem;
        font-family: $body-font;
        font-size: 1.6rem;
        line-height: 2.8rem;

        &::marker {
          color: $highlight-dark;
        }
      }
    }

    pre {
      width: 100%;
      background: $light-gray;
      border-radius: 0.8rem;
      overflow-x: scroll;
      padding: 1.6rem;
      margin-bottom: 3.2rem;

      code {
        width: 100%;
        display: table;
        font-family: monospace;
        font-size: 1.4rem;
        line-height: 1.8rem;
      }
    }

    blockquote {
      border-left: 0.4rem solid $light-green;
      padding-left: 1.6rem;
      font-family: $display-font;
      font-weight: 600;
      margin: 3.2rem 0 3.2rem 4rem;

      p {
        color: $gray;

        a {
          color: $highlight-dark;
          text-decoration: underline;
        }
      }
    }
  }

  @include responsive(mobile) {
    .thumb {
      width: 100%;
    }

    .description {
      width: 100%;
      border-radius: 0.8rem;
      > div {
        width: 100%;
        p {
          width: 100%;
        }
      }
    }
  }
}

.postsList {
  width: 38rem;
  max-height: 40rem;
  flex-direction: column;
  justify-content: flex-start;
  background: $bright-color;
  box-shadow: $shadow;
  border-radius: 1.6rem;
  position: absolute;
  top: 4rem;
  left: 0;
  z-index: 1000;
  box-sizing: border-box;
  padding: 1.6rem;
  opacity: 0;
  transform: translateY('-32px');
  transition: all 0.5s ease-out;

  > div {
    margin-bottom: 0.8rem;
  }

  h3 {
    text-transform: none !important;
  }
}

@include responsive(mobile) {
  .postsList {
    width: 20rem;
    right: 10rem !important;
  }
}

.list {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  justify-content: flex-start;

  > h2 {
    width: 100%;
    display: block;
    font-family: $display-font;
    color: $highlight-dark;
    font-size: 3.2rem;
    margin-bottom: 1.6rem;
    text-align: left;
  }

  .listItem {
    width: 100%;
    position: relative;
    z-index: 1;
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    justify-content: space-between;
    background: $bright-color;
    margin-bottom: 3.2rem;
    border-radius: 0.8rem;
    padding: 3.2rem;

    .thumb {
      width: 28rem;
      height: 20rem;
      display: block;

      img {
        width: 100%;
        max-width: 100%;
        height: auto;
      }
    }

    .heading {
      display: block;
      width: 100%;
      border-bottom: 0.1rem solid $mid-gray;
      font-family: $display-font;

      h2 {
        font-size: 2.8rem;
        line-height: 3.6rem;
        margin-top: 0;
        margin-bottom: 0.8rem;
      }

      h3 {
        width: 100%;
        display: block;
        font-family: $display-font;
        font-size: 1.4rem;
        margin-bottom: 1.6rem;
        color: $gray;
        font-weight: 400;
        padding-bottom: 0.8rem;

        span {
          color: $gray;
          font-weight: 600;
        }
      }
    }

    .contentBody {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      border-radius: 0 0.8rem 0.8rem 0;
      padding: 0;

      h2,
      h3,
      h4,
      h5,
      h6 {
        width: 100%;
        display: block;
        font-family: $display-font;
        color: $highlight-dark;
        margin: 0 0 1.6rem 0;
      }

      h2 {
        font-size: 3.2rem;
        line-height: 4rem;
      }

      h3 {
        font-size: 2.8rem;
        line-height: 3.6rem;
      }

      h4 {
        font-size: 2.4rem;
        line-height: 2.8rem;
      }

      h5 {
        font-size: 2.1rem;
        line-height: 2.4rem;
      }

      h6 {
        font-size: 1.8rem;
        line-height: 2.1rem;
      }

      p {
        font-family: $body-font;
        font-size: 1.6rem;
        line-height: 2.1rem;
        color: $base-color;

        img {
          max-width: 100%;
          height: auto;
          display: inline-block;
          margin: 1.6rem 0;
        }

        a {
          color: $highlight-dark;
          text-decoration: underline;

          &:hover {
            color: $highlight-color;
          }

          img {
            max-width: 100%;
            height: auto;
            display: inline-block;
            margin: 1.6rem 0;
          }
        }
      }

      img {
        max-width: 100%;
        height: auto;
        display: inline-block;
        margin: 1.6rem 0;
      }

      ul {
        list-style: none;
        padding: 0;
        margin-left: 3.2rem;
        margin-bottom: 3.2rem;

        li {
          position: relative;
          padding-left: 0.8rem;
          display: block;
          font-family: $body-font;
          font-size: 1.6rem;
          line-height: 2.8rem;

          &:before {
            content: '';
            width: 0.8rem;
            height: 0.8rem;
            display: block;
            border-radius: 50%;
            position: absolute;
            top: 1rem;
            left: -0.8rem;
            z-index: 1;
            background-color: $light-green;
          }
        }
      }

      ol {
        padding: 0;
        margin-left: 3.2rem;
        margin-bottom: 3.2rem;

        li {
          position: relative;
          margin-left: 0.8rem;
          font-family: $body-font;
          font-size: 1.6rem;
          line-height: 2.8rem;

          &::marker {
            color: $highlight-dark;
          }
        }
      }

      pre {
        width: 100%;
        background: $light-gray;
        border-radius: 0.8rem;
        overflow-x: scroll;
        padding: 1.6rem;
        margin-bottom: 3.2rem;

        code {
          width: 100%;
          display: table;
          font-family: monospace;
          font-size: 1.4rem;
          line-height: 1.8rem;
        }
      }

      blockquote {
        border-left: 0.4rem solid $light-green;
        padding-left: 1.6rem;
        font-family: $display-font;
        font-weight: 600;
        margin: 3.2rem 0 3.2rem 4rem;

        p {
          color: $gray;

          a {
            color: $highlight-dark;
            text-decoration: underline;
          }
        }
      }
    }

    @include responsive(mobile) {
      .thumb {
        width: 100%;
      }

      .description {
        width: 100%;
        border-radius: 0.8rem;
        > div {
          width: 100%;
          p {
            width: 100%;
          }
        }
      }
    }

    mark {
      line-height: 2.1rem;
    }
  }

  @include responsive(mobile) {
    > h2 {
      text-align: center;
    }
  }
}

.videoEmbedContainer {
  position: relative;
  padding-bottom: 56.25%;
  height: 0;
  overflow: hidden;
  max-width: 100%;
  width: 100%;
  border-radius: 0.8rem;

  iframe,
  object,
  embed {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 0.8rem;
  }
}
