@import 'theme';
@import 'mixins';

.lendersTable{
  width: 100%;
  cursor: grab;

  table{
    display: table;
    font-family: $display-font;
    background: $bright-color;
    padding: 3.2rem;
    border-radius: .8rem;

    thead, tbody{
      width: 100%;

      tr{
        th{
          font-size: 1.2rem;
          border-bottom: .2rem solid $mid-gray;
          height: 3.4rem;
          vertical-align: middle;
          line-height: 1.4rem;
          padding: 0 .8rem;
          text-align: left;
          color: $highlight-dark;
        }

        td{
          font-size: 1.2rem;
          border-bottom: .1rem solid $mid-gray;
          height: 3.4rem;
          vertical-align: middle;
          line-height: 1.4rem;
          padding: .4rem .8rem;
          transition: all .2s ease-out;

          &:nth-child(1){
            min-width: 10rem;
            font-weight: 700;
          }

          &:nth-child(2), &:nth-child(4), &:nth-child(5), &:nth-child(6), &:nth-child(7), &:nth-child(8), &:nth-child(9), &:nth-child(11), &:nth-child(12){
            min-width: 12rem;
          }

          &:nth-child(3), &:nth-child(15){
            min-width: 20rem;
          }
        }

        &:hover{
          td{
            background-color: $light-gray
          }
        }
      }    
    }
  }    
}

.ax_post_title {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  margin-top: 1.6rem;
}

.ax_post_info {
  width: 100%;
  display: table;

  .ax_post_details {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    justify-content: space-between;
    font-family: $display-font;
    background: $bright-color;
    padding: 3.2rem;
    border-radius: .8rem;

    .logo {
      width: 10rem;
      max-width: 10rem;
      height: 10rem;
      display: block;
    }
    
    .col{    
      width: calc(30% - 3.333rem);  
      display: block;      
      padding-left: 1.6rem;
      font-size: 1rem;
      transition: all .2s ease-out;
      border-left: .1rem solid $mid-gray;

      &:first-child{
        border-left: none;
      }

      p{
        font-size: 1.4rem;
        line-height: 2.1rem;
        display: block;
        transition: all .2s ease-out;
        padding: .4rem;
        border-radius: .3rem;

        &:hover{
          background-color: $light-gray
        }

        strong {
          color: $highlight-dark;
        }
      }
      
    }
  }
}

.ax_toggle_view{
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  height: 4rem;

  button {
    width: auto;
    border: .1rem solid $mid-gray;
    appearance: none;
    background: transparent;
    color: $base-color;
    margin: 0 1.6rem 0 0;
    border-radius: .3rem;   
    cursor: pointer; 

    svg {
      width: 1.6rem;
      height: 1.6rem;
      display: inline-block;
      vertical-align: top;
      margin-right: .4rem;
    }
  }

  button.active{
    background: $highlight-color;
    color: $bright-color;
  }
}

.ax_card_view {
  width: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}

@include responsive(desktop){
  .ax_add_broker_form{  
    width: 100rem;
  }
}

@include responsive(tablet){
  .ax_add_broker_form{  
    width: 70rem;

    .ax_field{
      width: 100%;
    }
  }
}

@include responsive(mobile){
  .ax_add_broker_form{  
    width: 100%;

    .ax_field{
      width: 100%;
    }
  }
}
