@import"https://fonts.googleapis.com/css2?family=Montserrat:wght@700&family=Open+Sans:ital,wght@0,400;0,700;1,400;1,700&display=swap";@media screen and (min-width: 0)and (max-width: 767px){.ax_section p{font-size:1.6rem;font-family:"Open Sans",sans-serif}.ax_container{width:90%;display:block;margin:0 auto}.ax_container p{font-family:"Open Sans",sans-serif;font-size:1.4rem;color:#000}}@media screen and (min-width: 768px)and (max-width: 1199px){.ax_section p{font-size:1.6rem;font-family:"Open Sans",sans-serif}.ax_container{width:74rem;display:block;margin:0 auto}}@media screen and (min-width: 1200px){.ax_section p{font-size:1.6rem;font-family:"Open Sans",sans-serif}.ax_container{width:114rem;display:block;margin:0 auto}}.ax_page_title{font-family:"Montserrat",sans-serif;font-size:2.4rem;margin:1.6rem 0 .8rem 0;color:#000;text-transform:capitalize}.ax_page_subtitle{font-family:"Montserrat",sans-serif;font-size:1.8rem;color:#2a7a94}.ax_form p a{font-weight:600;color:#2a7a94;text-decoration:underline}.ax_form .ax_form_heading{width:100%;margin-bottom:1.6rem}.ax_form .ax_form_heading p{font-size:1.6rem;font-family:"Open Sans",sans-serif;margin-top:0}.ax_form .ax_field{display:flex;flex-direction:column;position:relative}.ax_form .ax_field h1,.ax_form .ax_field h2,.ax_form .ax_field h3,.ax_form .ax_field h4,.ax_form .ax_field h5,.ax_form .ax_field h6{font-family:"Montserrat",sans-serif;margin-top:0;color:#2a7a94}.ax_form .ax_field label{color:#2a7a94;font-family:"Montserrat",sans-serif;font-size:1.4rem;font-weight:700;display:block;width:100%;margin-bottom:.4rem}.ax_form .ax_field label span{color:red;font-weight:800;font-size:1.6rem;line-height:1.6rem}.ax_form .ax_field label p{margin-top:0}.ax_form .ax_field label .counter{width:100%;display:block}.ax_form .ax_field label .counter span{font-size:1rem;font-weight:500;font-family:"Montserrat",sans-serif}.ax_form .ax_field textarea,.ax_form .ax_field input[type=text],.ax_form .ax_field input[type=email],.ax_form .ax_field input[type=password],.ax_form .ax_field input[type=number],.ax_form .ax_field input[type=tel],.ax_form .ax_field input[type=date],.ax_form .ax_field select{width:100%;border-radius:.4rem;border:.1rem solid #d7dfe9;background:#f2f5f9;padding:0 4rem 0 1.6rem;color:#000;font-family:"Montserrat",sans-serif;font-size:1.4rem;display:inline-block;margin-bottom:1.6rem}.ax_form .ax_field input[type=text],.ax_form .ax_field input[type=email],.ax_form .ax_field input[type=password],.ax_form .ax_field input[type=number],.ax_form .ax_field input[type=tel],.ax_form .ax_field input[type=date],.ax_form .ax_field input[type=date],.ax_form .ax_field input[type=time],.ax_form .ax_field input[type=number],.ax_form .ax_field input[type=url],.ax_form .ax_field select{height:4rem}.ax_form .ax_field input[type=text]::-moz-placeholder, .ax_form .ax_field input[type=email]::-moz-placeholder, .ax_form .ax_field input[type=password]::-moz-placeholder, .ax_form .ax_field input[type=number]::-moz-placeholder, .ax_form .ax_field input[type=tel]::-moz-placeholder, .ax_form .ax_field input[type=date]::-moz-placeholder, .ax_form .ax_field input[type=date]::-moz-placeholder, .ax_form .ax_field input[type=time]::-moz-placeholder, .ax_form .ax_field input[type=number]::-moz-placeholder, .ax_form .ax_field input[type=url]::-moz-placeholder, .ax_form .ax_field select::-moz-placeholder{font-weight:400;color:#d7dfe9}.ax_form .ax_field input[type=text]::placeholder,.ax_form .ax_field input[type=email]::placeholder,.ax_form .ax_field input[type=password]::placeholder,.ax_form .ax_field input[type=number]::placeholder,.ax_form .ax_field input[type=tel]::placeholder,.ax_form .ax_field input[type=date]::placeholder,.ax_form .ax_field input[type=date]::placeholder,.ax_form .ax_field input[type=time]::placeholder,.ax_form .ax_field input[type=number]::placeholder,.ax_form .ax_field input[type=url]::placeholder,.ax_form .ax_field select::placeholder{font-weight:400;color:#d7dfe9}.ax_form .ax_field textarea{padding:1.6rem;min-height:16rem}.ax_form .ax_field button.see{position:absolute;top:2.2rem;right:0;width:4rem;height:4rem;border:none;background:rgba(0,0,0,0)}.ax_form .ax_field button.see svg{color:#414141}.ax_form .ax_field small{font-family:"Montserrat",sans-serif;font-size:1.4rem}.ax_form .ax_field input[type=checkbox]{margin-bottom:1.6rem;position:relative;-webkit-appearance:none;-moz-appearance:none;appearance:none}.ax_form .ax_field input[type=submit]{height:4rem;border-radius:.4rem;background:#3caba7;color:#fff;font-family:"Montserrat",sans-serif;font-size:1.6rem;font-weight:600;border:none}.ax_form input[type=checkbox]{-webkit-appearance:none;-moz-appearance:none;appearance:none;font-size:1.6rem;line-height:1.6rem;text-align:center;width:1.6rem;height:1.6rem;border-radius:.2rem;border:.2rem solid #d7dfe9;background:#f2f5f9;display:inline-block;position:relative;vertical-align:middle;margin-bottom:.4rem !important}.ax_form input[type=checkbox]:checked:before{content:"✓";font-size:1.6rem;line-height:1.6rem;text-align:center;width:1.2rem;height:1.2rem;border-radius:.2rem;background:rgba(0,0,0,0);border:none;display:block;position:absolute;left:0;top:0;z-index:50}.ax_field_with_icon{display:flex;flex-wrap:wrap;flex-direction:row;justify-content:flex-start}.ax_field_with_icon .iconInput{width:100%;display:block;position:relative}.ax_field_with_icon .iconInput>svg,.ax_field_with_icon .iconInput>img{position:absolute;top:.6rem;left:.6rem;z-index:10}.ax_field_with_icon h1,.ax_field_with_icon h2,.ax_field_with_icon h3,.ax_field_with_icon h4,.ax_field_with_icon h5,.ax_field_with_icon h6{font-family:"Montserrat",sans-serif;margin-top:0;color:#2a7a94}.ax_field_with_icon label{color:#2a7a94;font-family:"Montserrat",sans-serif;font-size:1.4rem;font-weight:700;display:block;width:100%;margin-bottom:.4rem}.ax_field_with_icon label span{color:red;font-weight:800;font-size:1.6rem}.ax_field_with_icon label p{margin-top:0}.ax_field_with_icon textarea,.ax_field_with_icon input[type=text],.ax_field_with_icon input[type=email],.ax_field_with_icon input[type=password],.ax_field_with_icon input[type=number],.ax_field_with_icon input[type=tel],.ax_field_with_icon input[type=date],.ax_field_with_icon select{width:100%;border-radius:.4rem;border:.1rem solid #d7dfe9;background:#f2f5f9;padding:0 4rem 0 3.8rem;color:#000;font-family:"Montserrat",sans-serif;font-size:1.4rem;display:inline-block;margin-bottom:1.6rem}.ax_field_with_icon input[type=text],.ax_field_with_icon input[type=email],.ax_field_with_icon input[type=password],.ax_field_with_icon input[type=number],.ax_field_with_icon input[type=tel],.ax_field_with_icon input[type=date],.ax_field_with_icon input[type=date],.ax_field_with_icon input[type=time],.ax_field_with_icon input[type=number],.ax_field_with_icon input[type=url],.ax_field_with_icon select{height:4rem}.ax_field_with_icon input[type=text]::-moz-placeholder, .ax_field_with_icon input[type=email]::-moz-placeholder, .ax_field_with_icon input[type=password]::-moz-placeholder, .ax_field_with_icon input[type=number]::-moz-placeholder, .ax_field_with_icon input[type=tel]::-moz-placeholder, .ax_field_with_icon input[type=date]::-moz-placeholder, .ax_field_with_icon input[type=date]::-moz-placeholder, .ax_field_with_icon input[type=time]::-moz-placeholder, .ax_field_with_icon input[type=number]::-moz-placeholder, .ax_field_with_icon input[type=url]::-moz-placeholder, .ax_field_with_icon select::-moz-placeholder{font-weight:400;color:#d7dfe9}.ax_field_with_icon input[type=text]::placeholder,.ax_field_with_icon input[type=email]::placeholder,.ax_field_with_icon input[type=password]::placeholder,.ax_field_with_icon input[type=number]::placeholder,.ax_field_with_icon input[type=tel]::placeholder,.ax_field_with_icon input[type=date]::placeholder,.ax_field_with_icon input[type=date]::placeholder,.ax_field_with_icon input[type=time]::placeholder,.ax_field_with_icon input[type=number]::placeholder,.ax_field_with_icon input[type=url]::placeholder,.ax_field_with_icon select::placeholder{font-weight:400;color:#d7dfe9}.ax_field_with_icon textarea{padding:1.6rem;min-height:16rem}.ax_field_with_icon button.see{position:absolute;top:2.2rem;right:0;width:4rem;height:4rem;border:none;background:rgba(0,0,0,0)}.ax_field_with_icon button.see svg{color:#414141}.ax_field_with_icon small{font-family:"Montserrat",sans-serif;font-size:1.4rem}.ax_field_with_icon input[type=checkbox]{margin-bottom:1.6rem}.ax_field_with_icon input[type=submit]{height:4rem;border-radius:.4rem;background:#3caba7;color:#fff;font-family:"Montserrat",sans-serif;font-size:1.6rem;font-weight:600;border:none}.inputDanger{border:.2rem solid #de1600 !important}.ax_datepicker{width:100%;height:4rem;border-radius:.4rem;border:.1rem solid #d7dfe9;background:#f2f5f9;padding:0 4rem 0 1.6rem;color:#000;font-family:"Montserrat",sans-serif;font-size:1.4rem;display:inline-block;margin-bottom:1.6rem}.ax_datepicker::-moz-placeholder{font-weight:400;color:#d7dfe9}.ax_datepicker::placeholder{font-weight:400;color:#d7dfe9}.ax_datepicker .datepickerToggle{display:inline-block;position:relative;width:18px;height:19px}.ax_datepicker .datepickerToggleButton{position:absolute;right:.8rem;top:.6rem;width:100%;height:100%;background:blue}.ax_datepicker .datepickerInput{position:absolute;left:0;top:0;width:100%;height:100%;opacity:0;cursor:pointer;box-sizing:border-box}.ax_datepicker .datepickerInput::-webkit-calendar-picker-indicator{position:absolute;left:0;top:0;width:100%;height:100%;margin:0;padding:0;cursor:pointer}.ax_card_list{width:100%;display:flex;flex-direction:row;justify-content:flex-start;flex-wrap:wrap}.boxedLoading{width:100%;height:100%;background:rgba(170,247,166,.8);display:flex;flex-direction:row;justify-content:center;align-items:center;position:absolute;left:0;top:0;z-index:500}.primary{color:#3caba7}.base{color:#000}.purple{color:#7020bc}.orange{color:#e2591b}.green{color:#8eb440}.yellow{color:#e6b800}.blue{color:#2d4bd2}.teal{color:#00a8a8}.img_responsive{width:100%;max-width:100%;height:auto}.textCenter{text-align:center}.textRight{text-align:right}.centerContent{width:100%;justify-content:center !important;align-items:center !important}.validation{padding:1.6rem;background:rgba(255,0,0,.1);border:.1rem solid rgba(255,0,0,.3);border-radius:.8rem;margin-bottom:3.2rem}.validation h3{font-family:"Montserrat",sans-serif;font-size:1.6rem;margin:0 0 1.6rem 0;color:#a13}.validation ul{list-style:none;padding:0;margin:0}.validation ul li{padding-left:.8rem;position:relative;color:#a13;font-family:"Montserrat",sans-serif;font-size:1.2rem;line-height:1.8rem;font-weight:600}.validation ul li:before{content:"";width:.4rem;height:.4rem;display:block;border-radius:50%;background:#a13;position:absolute;top:.6rem;left:0}.dragzone{width:100%;height:10rem;height:auto;display:block;position:relative;z-index:1}.dragzone span{width:100%;line-height:4.8rem;font-family:"Montserrat",sans-serif;color:#666;font-weight:700;pointer-events:none;position:absolute;top:28%;text-align:center;z-index:1;transition:all .2s ease-out}.dragzone .dragarea{width:100%;height:10rem;display:flex;justify-content:center;align-items:center;position:relative;background:#f2f5f9;border-radius:.8rem;border:.2rem dashed #d7dfe9;z-index:10;transition:all .2s ease-out;cursor:grab}.dragzone .dragarea:hover{background:#a6b3c4;cursor:grab}.dragzone .dragarea:hover span{color:#fff}.contentBox{padding:3.2rem;background:#fff;border-radius:.8rem;margin-bottom:3.2rem;width:100%;display:flex;flex-wrap:wrap}.contentBox h2,.contentBox h3,.contentBox h4,.contentBox h5,.contentBox h6{width:100%;display:block;font-family:"Montserrat",sans-serif;color:#000;margin:0 0 1.6rem 0}.contentBox h2{font-size:2.4rem;line-height:2.8rem}.contentBox h3{font-size:2.1rem;line-height:2.4rem}.contentBox h4{font-size:1.8rem;line-height:2.1rem}.contentBox h5{font-size:1.6rem;line-height:2.1rem}.contentBox h6{font-size:1.4rem;line-height:1.8rem}.contentBox p{font-family:"Open Sans",sans-serif;font-size:1.6rem;line-height:2.1rem;color:#000;margin:0 0 1.6rem 0}.contentBox p img{max-width:100%;height:auto;display:inline-block}.contentBox p a{color:#2a7a94;text-decoration:underline}.contentBox p a:hover{color:#3caba7}.contentBox p a img{max-width:100%;height:auto;display:inline-block}.contentBox img{max-width:100%;height:auto;display:inline-block}.contentBox ul{list-style:none;padding:0;margin-left:3.2rem;margin-bottom:3.2rem}.contentBox ul li{position:relative;padding-left:.8rem;display:block;font-family:"Open Sans",sans-serif;font-size:1.6rem;line-height:2.8rem}.contentBox ul li:before{content:"";width:.8rem;height:.8rem;display:block;border-radius:50%;position:absolute;top:1rem;left:-0.8rem;z-index:1;background-color:#aae2e0}.contentBox ol{padding:0;margin-left:3.2rem;margin-bottom:3.2rem}.contentBox ol li{position:relative;margin-left:.8rem;font-family:"Open Sans",sans-serif;font-size:1.6rem;line-height:2.8rem}.contentBox ol li::marker{color:#2a7a94}.contentBox pre{width:100%;background:#f2f5f9;border-radius:.8rem;overflow-x:scroll;padding:1.6rem;margin-bottom:3.2rem}.contentBox pre code{width:100%;display:table;font-family:monospace;font-size:1.4rem;line-height:1.8rem}.contentBox blockquote{border-left:.4rem solid #aae2e0;padding-left:1.6rem;font-family:"Montserrat",sans-serif;font-weight:600;margin:3.2rem 0 3.2rem 4rem}.contentBox blockquote p{color:#666}.contentBox blockquote p a{color:#2a7a94;text-decoration:underline}.sepparator{width:100%;height:.2rem;background:#d7dfe9;border-radius:.1rem;display:block}.forceParagraph{font-size:1.4rem !important;font-weight:400 !important;font-family:"Open Sans",sans-serif !important}.ax_tip{font-family:"Montserrat",sans-serif;border-left:.4rem solid #3caba7;background:#f2f5f9;color:#000;border-radius:0 .8rem .8rem 0;padding:.8rem}.ax_tip span{color:#3caba7}.ax_tip_error{font-family:"Montserrat",sans-serif;border-left:.4rem solid red;background:#f2f5f9;color:#540000;border-radius:0 .8rem .8rem 0;padding:.8rem}.ax_tip_error span{color:#3caba7}.alertBox{margin:auto;width:40rem;height:auto;border-radius:.8rem;background:#fff;grid-template-columns:37rem 3rem;grid-template-areas:"title close" "content content" "footer footer";position:relative;top:-15rem;display:grid}.alertBox h3{grid-area:title;width:calc(100% - 2rem);font-family:"Montserrat",sans-serif;font-size:1.6rem;margin:0;padding:1.6rem 0 0 1.6rem}.alertBox>button{width:2rem;height:2rem;grid-area:button;border:none;color:#3caba7;background:#d7dfe9;border-radius:50%;padding:0;cursor:pointer;grid-area:close;margin:.8rem 0 0 0;transition:all .2s ease-out}.alertBox>button svg{position:relative;top:.2rem;fill:#000}.alertBox>button:hover{background:red}.alertBox>button:hover svg{fill:#fff}.alertBox .content{grid-area:content;width:100%;padding:0 1.6rem}.alertBox .content p{font-family:"Open Sans",sans-serif;font-size:1.4rem;color:#000}.alertBox footer{grid-area:footer;display:flex;justify-content:space-between;align-items:center;height:5.6rem;padding:.8rem 1.6rem 1.6rem 1.6rem}.alertBox footer button{width:auto;background:#3caba7;color:#fff;height:3.4rem;margin-bottom:.8rem;font-family:"Montserrat",sans-serif;font-weight:600;padding:.8rem 2rem;border:none;border-radius:.4rem;cursor:pointer;transition:all .2s ease-out}.alertBox footer button:hover{background:#3ba9a5}.article{width:100%;display:flex;flex-wrap:wrap;border-radius:.8rem;background:#fff;padding:3.2rem;margin:3.2rem 0}.article .heading{display:block;width:100%;border-bottom:.1rem solid #d7dfe9;font-family:"Montserrat",sans-serif}.article .heading h2{font-size:2.8rem;line-height:3.6rem;margin-top:0;margin-bottom:.8rem}.article .heading h3{width:100%;display:block;font-family:"Montserrat",sans-serif;font-size:1.4rem;margin-bottom:1.6rem;color:#666;font-weight:400;padding-bottom:.8rem}.article .heading h3 span{color:#666;font-weight:600}.article .contentBody{width:100%;display:flex;flex-wrap:wrap;border-radius:0 .8rem .8rem 0;padding:0}.article .contentBody h2,.article .contentBody h3,.article .contentBody h4,.article .contentBody h5,.article .contentBody h6{width:100%;display:block;font-family:"Montserrat",sans-serif;color:#2a7a94;margin:0 0 1.6rem 0}.article .contentBody h2{font-size:2.4rem;line-height:2.8rem}.article .contentBody h3{font-size:2.1rem;line-height:2.4rem}.article .contentBody h4{font-size:1.8rem;line-height:2.1rem}.article .contentBody h5{font-size:1.6rem;line-height:2.1rem}.article .contentBody h6{font-size:1.4rem;line-height:1.8rem}.article .contentBody p{font-family:"Open Sans",sans-serif;font-size:1.6rem;line-height:2.1rem;color:#000;margin:0 0 1.6rem 0}.article .contentBody p img{max-width:100%;height:auto;display:inline-block}.article .contentBody p a{color:#2a7a94;text-decoration:underline}.article .contentBody p a:hover{color:#3caba7}.article .contentBody p a img{max-width:100%;height:auto;display:inline-block}.article .contentBody img{max-width:100%;height:auto;display:inline-block}.article .contentBody ul{list-style:none;padding:0;margin-left:3.2rem;margin-bottom:3.2rem}.article .contentBody ul li{position:relative;padding-left:.8rem;display:block;font-family:"Open Sans",sans-serif;font-size:1.6rem;line-height:2.8rem}.article .contentBody ul li:before{content:"";width:.8rem;height:.8rem;display:block;border-radius:50%;position:absolute;top:1rem;left:-0.8rem;z-index:1;background-color:#aae2e0}.article .contentBody ol{padding:0;margin-left:3.2rem;margin-bottom:3.2rem}.article .contentBody ol li{position:relative;margin-left:.8rem;font-family:"Open Sans",sans-serif;font-size:1.6rem;line-height:2.8rem}.article .contentBody ol li::marker{color:#2a7a94}.article .contentBody pre{width:100%;background:#f2f5f9;border-radius:.8rem;overflow-x:scroll;padding:1.6rem;margin-bottom:3.2rem}.article .contentBody pre code{width:100%;display:table;font-family:monospace;font-size:1.4rem;line-height:1.8rem}.article .contentBody blockquote{border-left:.4rem solid #aae2e0;padding-left:1.6rem;font-family:"Montserrat",sans-serif;font-weight:600;margin:3.2rem 0 3.2rem 4rem}.article .contentBody blockquote p{color:#666}.article .contentBody blockquote p a{color:#2a7a94;text-decoration:underline}@media screen and (min-width: 0)and (max-width: 767px){.article .thumb{width:100%}.article .description{width:100%;border-radius:.8rem}.article .description>div{width:100%}.article .description>div p{width:100%}}.postsList{width:38rem;max-height:40rem;flex-direction:column;justify-content:flex-start;background:#fff;box-shadow:2px 2px 8px rgba(45,44,48,.15);border-radius:1.6rem;position:absolute;top:4rem;left:0;z-index:1000;box-sizing:border-box;padding:1.6rem;opacity:0;transform:translateY("-32px");transition:all .5s ease-out}.postsList>div{margin-bottom:.8rem}.postsList h3{text-transform:none !important}@media screen and (min-width: 0)and (max-width: 767px){.postsList{width:20rem;right:10rem !important}}.list{width:100%;display:flex;flex-wrap:wrap;flex-direction:row;justify-content:flex-start}.list>h2{width:100%;display:block;font-family:"Montserrat",sans-serif;color:#2a7a94;font-size:3.2rem;margin-bottom:1.6rem;text-align:left}.list .listItem{width:100%;position:relative;z-index:1;display:flex;flex-wrap:wrap;flex-direction:row;justify-content:space-between;background:#fff;margin-bottom:3.2rem;border-radius:.8rem;padding:3.2rem}.list .listItem .thumb{width:28rem;height:20rem;display:block}.list .listItem .thumb img{width:100%;max-width:100%;height:auto}.list .listItem .heading{display:block;width:100%;border-bottom:.1rem solid #d7dfe9;font-family:"Montserrat",sans-serif}.list .listItem .heading h2{font-size:2.8rem;line-height:3.6rem;margin-top:0;margin-bottom:.8rem}.list .listItem .heading h3{width:100%;display:block;font-family:"Montserrat",sans-serif;font-size:1.4rem;margin-bottom:1.6rem;color:#666;font-weight:400;padding-bottom:.8rem}.list .listItem .heading h3 span{color:#666;font-weight:600}.list .listItem .contentBody{width:100%;display:flex;flex-wrap:wrap;border-radius:0 .8rem .8rem 0;padding:0}.list .listItem .contentBody h2,.list .listItem .contentBody h3,.list .listItem .contentBody h4,.list .listItem .contentBody h5,.list .listItem .contentBody h6{width:100%;display:block;font-family:"Montserrat",sans-serif;color:#2a7a94;margin:0 0 1.6rem 0}.list .listItem .contentBody h2{font-size:3.2rem;line-height:4rem}.list .listItem .contentBody h3{font-size:2.8rem;line-height:3.6rem}.list .listItem .contentBody h4{font-size:2.4rem;line-height:2.8rem}.list .listItem .contentBody h5{font-size:2.1rem;line-height:2.4rem}.list .listItem .contentBody h6{font-size:1.8rem;line-height:2.1rem}.list .listItem .contentBody p{font-family:"Open Sans",sans-serif;font-size:1.6rem;line-height:2.1rem;color:#000}.list .listItem .contentBody p img{max-width:100%;height:auto;display:inline-block;margin:1.6rem 0}.list .listItem .contentBody p a{color:#2a7a94;text-decoration:underline}.list .listItem .contentBody p a:hover{color:#3caba7}.list .listItem .contentBody p a img{max-width:100%;height:auto;display:inline-block;margin:1.6rem 0}.list .listItem .contentBody img{max-width:100%;height:auto;display:inline-block;margin:1.6rem 0}.list .listItem .contentBody ul{list-style:none;padding:0;margin-left:3.2rem;margin-bottom:3.2rem}.list .listItem .contentBody ul li{position:relative;padding-left:.8rem;display:block;font-family:"Open Sans",sans-serif;font-size:1.6rem;line-height:2.8rem}.list .listItem .contentBody ul li:before{content:"";width:.8rem;height:.8rem;display:block;border-radius:50%;position:absolute;top:1rem;left:-0.8rem;z-index:1;background-color:#aae2e0}.list .listItem .contentBody ol{padding:0;margin-left:3.2rem;margin-bottom:3.2rem}.list .listItem .contentBody ol li{position:relative;margin-left:.8rem;font-family:"Open Sans",sans-serif;font-size:1.6rem;line-height:2.8rem}.list .listItem .contentBody ol li::marker{color:#2a7a94}.list .listItem .contentBody pre{width:100%;background:#f2f5f9;border-radius:.8rem;overflow-x:scroll;padding:1.6rem;margin-bottom:3.2rem}.list .listItem .contentBody pre code{width:100%;display:table;font-family:monospace;font-size:1.4rem;line-height:1.8rem}.list .listItem .contentBody blockquote{border-left:.4rem solid #aae2e0;padding-left:1.6rem;font-family:"Montserrat",sans-serif;font-weight:600;margin:3.2rem 0 3.2rem 4rem}.list .listItem .contentBody blockquote p{color:#666}.list .listItem .contentBody blockquote p a{color:#2a7a94;text-decoration:underline}@media screen and (min-width: 0)and (max-width: 767px){.list .listItem .thumb{width:100%}.list .listItem .description{width:100%;border-radius:.8rem}.list .listItem .description>div{width:100%}.list .listItem .description>div p{width:100%}}.list .listItem mark{line-height:2.1rem}@media screen and (min-width: 0)and (max-width: 767px){.list>h2{text-align:center}}.videoEmbedContainer{position:relative;padding-bottom:56.25%;height:0;overflow:hidden;max-width:100%;width:100%;border-radius:.8rem}.videoEmbedContainer iframe,.videoEmbedContainer object,.videoEmbedContainer embed{position:absolute;top:0;left:0;width:100%;height:100%;border-radius:.8rem}/*# sourceMappingURL=Posts.module.min.css.map */