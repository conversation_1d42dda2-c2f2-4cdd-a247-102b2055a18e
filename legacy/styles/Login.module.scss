@import 'theme';
@import 'mixins';

.ax_login {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  overflow: hidden;
}

.login_bg_icon {
  width: 12rem;
  height: 12rem;
  display: block;
  position: absolute;
  right: calc(30% + 15rem);
  top: 15rem;
  z-index: 100;
  background-image: url('../public/images/bg-login-graphic-icon.svg');
  background-size: contain;
  background-repeat: no-repeat;
  transform: rotate(-25deg);
  pointer-events: none;
}

.login_bg_1 {
  width: 64.3rem;
  height: 60.7rem;
  display: block;
  position: absolute;
  right: -15rem;
  bottom: -25rem;
  z-index: 100;
  background-image: url('../public/images/bg-login-graphic.svg');
  background-size: contain;
  background-repeat: no-repeat;
  pointer-events: none;
}

.login_bg_2 {
  width: 64.3rem;
  height: 60.7rem;
  display: block;
  position: absolute;
  left: 0;
  top: -30rem;
  z-index: 100;
  background-image: url('../public/images/bg-login-graphic.svg');
  background-size: contain;
  background-repeat: no-repeat;
  transform-origin: center;
  transform: rotate(135deg);
  pointer-events: none;
}

.ax_login_left_column {
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  width: 70%;
  justify-content: space-between;
  align-items: center;
  background-color: $mid-gray;
  background-image: url('../public/images/bg-login.jpg');
  background-size: cover;
  overflow: hidden;
  position: relative;

  .topbar {
    width: 100%;
    height: 7rem;
    display: block;
    position: relative;

    span {
      position: absolute;
      top: 8rem;
      left: 38%;
      font-family: $display-font;
      font-size: 4.1rem;
      font-weight: 700;
      text-transform: uppercase;
      color: #fff;
    }

    img {
      width: 40rem;
      height: auto;
      position: absolute;
      top: 4rem;
      left: 15%;
    }
  }

  .footer {
    width: 100%;
    display: block;
    padding-left: 15%;
    padding-bottom: 10rem;
    h2 {
      width: 45%;
      font-family: $display-font;
      font-size: 4.1rem;
      font-weight: 700;
      color: #fff;
      font-style: italic;
    }
    h4 {
      font-family: $display-font;
      font-size: 2.1rem;
      font-weight: 700;
      color: #fff;
    }
  }

  @media screen and (min-width: 1280px) and (max-width: 1439px) {
    .topbar {
      span {
        left: 38%;
        font-size: 3rem;
      }

      img {
        width: 30rem;
      }
    }

    .footer {
      h2 {
        width: 65%;
        font-size: 3.2rem;
      }
    }
  }

  @media screen and (min-width: 1440px) and (max-width: 1640px) {
    .topbar {
      span {
        left: 38%;
      }

      img {
        width: 34rem;
      }
    }

    .footer {
      h2 {
        width: 65%;
        font-size: 3.6rem;
      }
    }
  }
}

.ax_login_right_column {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 30%;
  padding: 3.2rem;

  > p {
    text-align: center;
    font-family: $display-font;
    font-weight: 600;
    color: $base-color;
    font-size: 1.8rem;
    letter-spacing: 0.1rem;
  }

  img {
    width: 20rem;
    height: auto;
  }

  .ax_login_form {
    width: 90%;
    color: $highlight-color;

    label {
      font-family: $display-font;
      font-size: 1.2rem;
      margin-bottom: 0.4rem;
      display: block;
    }

    input[type='email'],
    input[type='password'],
    input[type='text'] {
      width: 100%;
      height: 4rem;
      border-radius: 0.4rem;
      color: $base-color;
      font-family: $display-font;
      font-weight: 400;
      margin-bottom: 3.2rem;
      padding: 0 3.2rem 0 1.6rem;
      background: #e6edf5;

      &::placeholder {
        color: #bec7d1;
      }
    }

    button {
      width: 100%;
      display: block;
      background-color: $highlight-color;
      height: 4rem;
      border: none;
      border-radius: 0.4rem;
      color: $bright-color;
      font-size: 1.6rem;
      font-family: $display-font;
      transition: all 0.2s cubic-bezier(0.075, 0.82, 0.165, 1);
      cursor: pointer;
      font-weight: 600;

      > img,
      svg {
        display: inline-block;
        width: 3.6rem !important;
        height: 3.6rem !important;
        vertical-align: middle;
      }

      &:hover {
        background-color: lighten($highlight-color, 3);
      }
    }

    button.btnShowPassword {
      position: absolute;
      top: 2rem;
      right: 0.4rem;
      z-index: 200;
      width: 3.6rem !important;
      height: 3.6rem !important;
      display: block;
      color: $base-mid-color;
      background: transparent;
      border: none;

      svg {
        width: 2.4rem !important;
        height: 2.4rem !important;
      }
    }
  }

  a {
    color: #000;
    transition: all 0.2s cubic-bezier(0.075, 0.82, 0.165, 1);
    font-family: $display-font;
    font-size: 1.4rem;

    &:hover {
      color: $highlight-color;
    }
  }
}

.formFooter {
  padding: 1.6rem;
}

.formError {
  display: flex;
  align-items: center;
  width: 1005;
  padding: 0.8rem 1.6rem;
  border-radius: 0.4rem;
  background: $red;
  margin: 0 0 3.2rem 0;

  svg {
    margin-right: 0.8rem;
    align-self: bottom;
  }

  p {
    display: flex;
    align-items: center;
    color: #fff;
    font-size: 1.4rem;
    line-height: 2.1rem;
    font-family: $display-font;
    font-weight: 600;
    margin: 0;
  }
}

@include responsive(tablet) {
  .ax_login_left_column {
    width: 55%;
  }

  .ax_login_right_column {
    width: 45%;

    .ax_login_form {
      padding: 1.6rem;
    }
  }
}

@include responsive(mobile) {
  .ax_login_left_column {
    display: none;
  }

  .ax_login_right_column {
    width: 100%;

    .ax_login_form {
      padding: 1.6rem;
    }
  }
}
