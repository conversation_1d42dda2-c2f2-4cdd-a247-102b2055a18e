@import 'theme';
@import 'mixins';

.ax_form_container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  margin-top: 10rem;
}

.ax_form{
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;  
  background: $bright-color;
  padding: 1.6rem;
  border-radius: .8rem;
  display: block;
  width: 60rem;
}

.ax_btn_submit{
  height: 4rem;
  border-radius: .4rem;
  background: $highlight-color;
  color: $bright-color;
  font-family: $display-font;
  font-size: 1.6rem;
  font-weight: 600;
  border: none;
  padding: 0 2.4rem;
  float: right;

  > img, svg{
    display: inline-block;
    width: 3.6rem !important;
    height: 3.6rem !important;
    vertical-align: middle;
  }
}

.ax_response {
  width: 100%;
  display: table;
  margin-top: 1.6rem;
  
  .message {
    margin-top: 1.6rem;
  
    p{
      a{
        font-weight: 800;
        color: $highlight-dark;
        text-decoration: underline;
      }
    }
  }
}

@include responsive(mobile){
  .ax_form{    
    width: 90%;
  }
}
