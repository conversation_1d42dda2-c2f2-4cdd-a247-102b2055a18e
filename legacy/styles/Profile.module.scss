@import 'theme';
@import 'mixins';

.ax_hero {
  width: 100%;
  height: 25rem;
  background: $bright-color;
  border-radius: 0.8rem;
  padding: 3.2rem;
  margin: 1.6rem 0;
  position: relative;
  z-index: 10;

  h1 {
    margin: 0;
  }
}

.ax_content {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-top: 1.6rem;

  h1,
  h2,
  h3 {
    font-family: $display-font;
  }

  h3 {
    font-size: 2.4rem;
    margin: 0 0 1.6rem 0;
  }

  p {
    font-family: $body-font;
    font-size: 1.4rem;
    line-height: 1.8rem;
    margin: 0 0 2.4rem 0;
  }
}

.ax_left_column {
  position: relative;
  width: 25rem;
  display: block;
  padding: 0;
  margin: 0;
  z-index: 50;
  overflow: hidden;
  border-radius: 0.8rem;
  background: #fff;
  align-self: flex-start;

  h3 {
    width: 100%;
    font-size: 1.6rem;
    color: $highlight-color;
  }

  .photo {
    width: 100%;
    display: block;
    position: relative;

    img {
      width: 100%;
      max-width: 100%;
      height: auto;
      border-radius: 0.8rem 0.8rem 0 0;
      background: $light-gray;
      display: block;
    }

    .photoLoading {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      display: table;
      text-align: center;
      background: white;
      border-bottom: 0.1rem solid $light-gray;

      img {
        width: 80%;
        max-width: 80%;
        height: auto;
      }

      p {
        font-family: $display-font;
        font-size: 1.6rem;
        color: $highlight-color;
        margin: 1.6rem 0;
        width: 100%;
        display: block;
        text-align: center;
      }
    }
  }

  .ax_info {
    padding: 1.6rem;

    h2,
    h3,
    h4,
    p {
      color: $base-color;
      font-family: $display-font;
      margin: 0 0 0.8rem 0;
      font-size: 1.6rem;

      span,
      a {
        color: $highlight-color;
      }
    }

    p {
      font-size: 1.4rem;
    }
  }
}

.ax_right_column {
  .content_left {
    width: 100%;
    background: $bright-color;
    border-radius: 0.8rem;
    padding: 3.2rem;

    .ax_form {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      justify-content: space-between;

      .ax_field {
        width: calc(50% - 1.6rem);
      }
    }

    @include responsive(desktop) {
      .ax_form {
        .ax_field {
          width: calc(50% - 1.6rem);
        }
      }
    }

    @include responsive(tablet) {
      .ax_form {
        .ax_field {
          width: 100%;
        }
      }
    }

    @include responsive(mobile) {
      .ax_form {
        .ax_field {
          width: 100%;
        }
      }
    }
  }

  width: calc(100% - 28rem);
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.ax_form {
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: $display-font;
  }
  p {
    font-family: $body-font;
    font-size: 1.4rem;
    line-height: 2.1rem;
    margin-top: 0;

    .checkbox {
      font-weight: 700;

      &:first-child {
        margin-right: 1.6rem;
      }

      &:last-child {
        margin-right: 2.4rem;
      }
    }
  }

  .photo {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 1.6rem;
    svg {
      margin: 3rem auto;
      fill: $mid-gray;
    }
  }
}

.phoneExt {
  display: flex;
  justify-content: space-between;

  div {
    &:nth-child(1) {
      width: 70%;
    }

    &:nth-child(2) {
      width: calc(30% - 1.6rem);
    }
  }
}

.ax_btn_submit {
  height: 4rem;
  border-radius: 0.4rem;
  background: $highlight-color;
  color: $bright-color;
  font-family: $display-font;
  font-size: 1.6rem;
  font-weight: 600;
  border: none;
  padding: 0 2.4rem;
  float: right;

  > img,
  svg {
    display: inline-block;
    width: 3.6rem !important;
    height: 3.6rem !important;
    vertical-align: middle;
  }
}

.ax_image_options {
  font-family: $display-font;
  font-size: 1.4rem;
  padding: 0.8rem;
  border-top: 0.1rem solid $light-gray;

  h3 {
    font-size: 1.6rem;
    color: $highlight-color;
  }

  input {
    width: 100%;
    height: 3.4rem;
    line-height: 3.4rem;
    margin: 1.6rem 0;
    position: relative;
    border-radius: 0.2rem;
    border: 0.4rem dashed $mid-gray;
    background-color: $light-gray;
  }

  label {
    width: 100%;
    display: block;
    font-size: 1.4rem;
    line-height: 1.4rem;
    color: $base-color;
    font-weight: 600;
  }

  button {
    height: 4rem;
    border-radius: 0.4rem;
    background: $highlight-color;
    color: $bright-color;
    font-family: $display-font;
    font-size: 1.6rem;
    font-weight: 600;
    border: none;
    padding: 0 2.4rem;
    width: 100%;
    margin-top: 0.8rem;
    > img,
    svg {
      display: inline-block;
      vertical-align: middle;
    }
  }
}

.badgeCard {
  width: auto;
  display: table;
  border-radius: 0.8rem;
  background: #fff;
}

.photoForm {
  input {
    width: 100%;
    height: 4rem;
  }

  button {
    width: 100%;
    background: $highlight-color;
    color: #fff;
    font-family: $display-font;
    font-weight: 600;
    border: none;
    height: 3.6rem;
    border-radius: 0.4rem;
    padding: 0.8rem 2.4rem;
    clear: both;
  }

  small {
    width: 100%;
    display: block;
    margin-top: 1.6rem;
  }
}

.sepparator {
  width: 100%;
  height: 0.1rem;
  background: $light-gray;
  display: block;
  margin: 1.6rem 0;
}

.qrCode {
  width: 100%;
  display: flex;
  justify-content: center;
  margin: 1.6rem 0;
}

@include responsive(tablet) {
  .ax_content {
    flex-wrap: wrap;
    .ax_left_column {
      order: 2;
    }
    .ax_right_column {
      order: 1;
    }
  }
}

@include responsive(mobile) {
  .ax_content {
    flex-wrap: wrap;
    .ax_left_column {
      width: 100%;
      order: 2;
    }
    .ax_right_column {
      width: 100%;
      order: 1;
      margin-bottom: 1.6rem;
    }
  }
}

.qrCodes,
.badges {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  margin: 3.2rem 0;

  h3 {
    width: 100%;
  }

  .ax_card_list {
    justify-content: flex-start !important;

    .ax_card_body {
      h3 {
        word-wrap: wrap;
      }
    }
  }
}

.badges {
  padding: 3.2rem;
  background: #fff;
  border-radius: 0.8rem;
}

.qrCodes {
  padding: 3.2rem;
  background: #fff;
  border-radius: 0.8rem;

  .ax_card_list {
    .ax_card_body {
      p,
      h3 {
        word-break: break-word;
      }
    }
  }
}

.description {
  ul {
    list-style: none;
    padding: 0;
    font-family: $display-font;
    font-size: 1.4rem;

    li {
      padding-left: 1rem;
      position: relative;
      height: 3.6rem;
      line-height: 3.6rem;

      &::before {
        content: '';
        width: 0.6rem;
        height: 0.6rem;
        border-radius: 50%;
        display: block;
        position: absolute;
        top: 1.5rem;
        left: 0;
        background: $highlight-color;
      }
    }
  }
}

.validation {
  padding: 1.6rem;
  background: transparentize(red, 0.9);
  border: 0.1rem solid transparentize(red, 0.7);
  border-radius: 0.8rem;
  margin-bottom: 3.2rem;

  h3 {
    font-family: $display-font;
    font-size: 1.6rem;
    margin: 0 0 1.6rem 0;
    color: #aa1133;
  }

  ul {
    list-style: none;
    padding: 0;
    margin: 0;

    li {
      padding-left: 0.8rem;
      position: relative;
      color: #aa1133;
      font-family: $display-font;
      font-size: 1.2rem;
      line-height: 1.8rem;
      font-weight: 600;

      &:before {
        content: '';
        width: 0.4rem;
        height: 0.4rem;
        display: block;
        border-radius: 50%;
        background: #aa1133;
        position: absolute;
        top: 0.6rem;
        left: 0;
      }
    }
  }
}

.sigContainer {
  display: table;
  border: none;
  width: 500px;
  margin: 0;
  padding: 0;
  border: 0.1rem solid $mid-gray;
}

.initialsContainer {
  display: table;
  border: none;
  width: 250px;
  margin: 0;
  padding: 0;
  border: 0.1rem solid $mid-gray;
}

.sigCanvas {
  width: 100%;
  display: table;
}

.formSectionTitle {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  margin-top: 1.6rem;

  h3 {
    font-size: 2.1rem;
    color: $base-color;
    font-family: $display-font;
    line-height: 2.4rem;
    margin: 0 0 0.4rem 0;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    position: relative;

    svg {
      color: $highlight-color;
      margin-right: 0.8rem;
    }

    // &:before {
    //   content: '';
    //   width: 1rem;
    //   height: 1rem;
    //   display: block;
    //   border-radius: 50%;
    //   background: $highlight-color;
    //   position: absolute;
    //   top: .7rem;
    //   left: 0;
    // }
  }

  h4 {
    font-size: 1.6rem;
    color: $base-color;
    font-family: $display-font;
    line-height: 1.8rem;
    margin: 0.4rem 0 0.8rem 0;
    display: block;
    color: $gray;
  }

  p {
    font-size: 1.4rem;
    color: $base-color;
    font-family: $body-font;
    line-height: 1.6rem;
    margin: 0.4rem 0 0.8rem 0;
  }

  .sepparator {
    width: 100%;
    height: 0.2rem;
    display: block;
    background: $light-gray;
    margin: 0 0 1.6rem 0;
  }
}

.counter {
  width: 100%;
  display: flex;
  justify-content: flex-end;

  span {
    font-size: 1rem;
    font-weight: 500;
    font-family: $display-font;
  }
}

.cropperModal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.cropperContent {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  z-index: 1001;
}

.closeButton {
  position: absolute;
  top: 12px;
  right: 10px;
  background: none;
  border: none;
  font-size: 24px;
  line-height: 4rem;
  cursor: pointer;
  z-index: 1002;
  background: black;
  color: #fff;
  padding: 0;
  border-radius: 0.4rem;
  width: 3.2rem;
  height: 3.2rem;
}

.cropperContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;

  .cropper {
    height: 400px;
    width: 100%;
    position: relative;
  }

  .controls {
    margin-top: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;

    label {
      margin-bottom: 5px;
      font-family: $display-font;
      font-size: 1.4rem;
      font-weight: 600;
    }
  }

  .zoomRange {
    width: 100%;
    margin-bottom: 20px;
  }

  .cropButton {
    width: 100%;
    padding: 1.2rem 1.6rem;
    background-color: $highlight-color;
    color: white;
    border: none;
    border-radius: 4px;
    font-family: $display-font;
    font-size: 1.6rem;
    cursor: pointer;

    &:hover {
      background-color: black;
    }
  }
}

.sigButtons {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1.6rem;
}
