@import 'theme';
@import 'mixins';

.ax_notification_list {
  width: 38rem;
  max-height: 40rem;
  flex-direction: column;
  justify-content: flex-start;
  background: $bright-color;
  box-shadow: $shadow;
  border-radius: 1.6rem;
  position: absolute;
  top: 4rem;
  left: 0;
  z-index: 1000;
  box-sizing: border-box;
  padding: 1.6rem;
  opacity: 0;
  transform: translateY('-32px');
  transition: all .5s ease-out;
  overflow-y: scroll;

  > div {
    margin-bottom: .8rem;
    position: relative;

    button{
      border: none;
      appearance: none;
      width: 1.6rem;
      height: 1.6rem;
      border-radius: 50%;
      background: black;
      color: #fff;
      text-align: center;      
      display: block;
      position: absolute;
      top: .4rem;
      right: .4rem;
      padding: 0;
      cursor: pointer;

      &:hover{
        
          background: $red;
        
      }

      svg{
        fill: white;
        transform: translateY(.1rem)        
      }
    }
  }

  h3{
    text-transform: none !important;
  }
}

.ax_notifications_visible{
  display: flex;
  opacity: 1;
  transform: translateY('0');
}

.ax_notifications_hidden{
  display: none;
}

@include responsive(mobile){
  .ax_notification_list {
    width: 20rem;    
    right: 10rem !important;
  }
}

.list{
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  justify-content: flex-start;  

  > h2{
    width: 100%;
    display: block;
    font-family: $display-font;
    color: $highlight-dark;
    font-size: 3.2rem;
    margin-bottom: 1.6rem;
    text-align: left;
  }
  
  .listItem{
    width: 100%;
    position: relative;
    z-index: 1;
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    justify-content: space-between;
    background: $bright-color;
    margin-bottom: 3.2rem;
    border-radius: .8rem; 
    padding: 3.2rem;    
    
    h3{
      width: 100%;
      display: block;
      font-family: $display-font;
      font-size: 1.4rem;
      margin-bottom: 0;
      color: $gray;
      font-weight: 400;
      padding-bottom: .8rem;
      border-bottom: .1rem solid $mid-gray;

      span{
        color: $gray;
        font-weight: 600;
      }
    }

    .description{
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      border-radius: 0 .8rem .8rem 0;
      padding: 0;

      h2{
        width: 100%;
        display: block;
        font-family: $display-font;
        font-size: 1.8rem;
        color: $highlight-dark;
        margin: 0;
      }

      p{
        font-family: $body-font;
        font-size: 1.6rem;
        color: $base-color;
      }
    }
    
  
    @include responsive(mobile){            
      .description{
        width: 100%;
        border-radius: .8rem;
        >div{
          width: 100%;
          p{
            width: 100%;
          }
        }
        
      }
    }
  }

  @include responsive(mobile){
    > h2{
      text-align: center;
    }
  }    
}