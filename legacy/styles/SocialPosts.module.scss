@import 'theme';
@import 'mixins';

.posts {
  width: 100%;
  display: flex;
  flex-wrap: wrap;

  h1{
    width: 100%;
    display: block;
    font-family: $display-font;
    font-size: 2.4rem;
    color: $base-color;
    line-height: 3.2rem;
  }
}

.canvas {
  width: 100rem;
  height: 100rem;
  display: block;
  border: 1px solid red;
  position: relative;
}

.handle {
  width: auto;
  cursor: move;
  display: table;
}

.dragglableInfo {
  font-family: $display-font;
  font-size: 1.6rem;
  padding: 1.6rem;
  display: table;
  width: auto;
  cursor: move;

  input {
    background: transparent;
    font-family: $display-font;
    font-size: 2.4rem;
    font-weight: 700;
    appearance: none;
    box-shadow: none;
    border: none;
  }
}

.actions {
  position: fixed;
  top: 12rem;
  right: 8rem;
  background: $bright-color;
  border-radius: 0.8rem;
  box-shadow: $shadow-large;
  padding: 1.2rem;
  z-index: 300;
  width: 30rem;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;

  label {
    font-family: $display-font;
    font-size: 1.4rem;
    color: $highlight-dark;
    font-weight: 600;

    input[type='checkbox'] {
      font-family: $display-font;
      font-size: 1.4rem;
      color: $base-color;
      font-weight: 400;
    }
  }

  .boundingBox {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;
  }
}

.posts{
  h2{
    margin: 0;
    width: 100%;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    font-family: $display-font;
    font-size: 2.4rem;
    color: $base-color;

    svg{
      margin-right: .5rem;
      transform: translateY(-.1rem)
    }
    
    span{      
      color: $highlight-color;      
    }
  }  
}

.caption{
  width: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: flex-start;
  margin: 1.6rem 0 3.2rem 0;
  background: $mid-gray;
  border-radius: .8rem;
  padding: 3.2rem;

  h2{    
    width: 100%;    
    font-family: $display-font;
    font-size: 2.1rem; 
    display: block; 
    margin: 0 0 1.6rem 0;
  }

  p{
    width: 100%;    
    font-family: $body-font;
    font-size: 1.4rem; 
    display: block; 
    margin: 0 0 .8rem 0;
  }

  h3{
    width: 100%;    
    font-family: $display-font;
    font-size: 1.6rem; 
    display: block; 
    margin: 1.6rem 0 0 0;
  }

  >div{
    width: 100%;    
    font-family: $body-font;
    font-size: 1.8rem;
    line-height: 2.4rem; 
    display: block; 
    margin: 1.6rem 0 2.4rem 0;
  }

  button{
    margin: 0;
  }
  
}

.imageList {
  padding: 0;
  list-style: none;
  width: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;


  .imageItem {
    display: block;
    height: auto;

    img {
      width: 100%;
      max-width: 100%;
      height: auto;
      display: block;
    }

    caption{
      width: 100%;
      display: block;
      text-align: left;
      font-family: $display-font;
      font-size: 1.4rem;
      line-height: 1.6rem;
      color: $base-color;
      font-weight: 600;
      margin: .4rem 0;
    }

  }

  @include responsive(mobile) {
    .imageItem {
      width: 100%;
      margin-bottom: 3.2rem;
      margin-right: 0;
    }
  }

  @include responsive(tablet) {
    .imageItem {
      width: calc(33% - 3.2rem);
      margin-right: 3.2rem;
      margin-bottom: 3.2rem;
    }
  }

  @include responsive(desktop) {
    .imageItem {
      width: calc(25% - 3.2rem);
      margin-right: 3.2rem;
      margin-bottom: 3.2rem;
    }
  }
}

.btnCopy {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 3.4rem;
  font-size: 1.2rem;
  color: $highlight-dark;
  font-weight: 600;
  letter-spacing: 0.1rem;
  border-radius: 0.4rem;
  border: none;
  background-color: $bright-color;
  transition: all 0.3s ease-out;
  padding: 0 1.6em;
  cursor: pointer;
  margin: 0 0 2.4rem 0;

  svg{
    margin-right: .8rem;
  }

  &:hover {
    background: $base-color;
    color: $bright-color;
  }
}

.btnDownload {
  height: 4rem;
  font-size: 1.6rem;
  color: $bright-color;
  font-weight: 600;
  letter-spacing: 0.1rem;
  border-radius: 0.4rem;
  border: none;
  background-color: $highlight-color;
  transition: all 0.3s ease-out;
  padding: 0 3.2rem;
  cursor: pointer;
  margin: 0 0 3.2rem 0;

  &:hover {
    background: $base-color;
    color: $bright-color;
  }
}

.calendar {
  width: 100%;
  display: flex;
  flex-wrap: wrap;

  h2{
    margin: 1.6rem 0;
    width: 100%;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    font-family: $display-font;
    font-size: 2.4rem;
    color: $base-color;
  }
}