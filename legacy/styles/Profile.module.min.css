@import"https://fonts.googleapis.com/css2?family=Montserrat:wght@700&family=Open+Sans:ital,wght@0,400;0,700;1,400;1,700&display=swap";@media screen and (min-width: 0)and (max-width: 767px){.ax_section p{font-size:1.6rem;font-family:"Open Sans",sans-serif}.ax_container{width:90%;display:block;margin:0 auto}.ax_container p{font-family:"Open Sans",sans-serif;font-size:1.4rem;color:#000}}@media screen and (min-width: 768px)and (max-width: 1199px){.ax_section p{font-size:1.6rem;font-family:"Open Sans",sans-serif}.ax_container{width:74rem;display:block;margin:0 auto}}@media screen and (min-width: 1200px){.ax_section p{font-size:1.6rem;font-family:"Open Sans",sans-serif}.ax_container{width:114rem;display:block;margin:0 auto}}.ax_page_title{font-family:"Montserrat",sans-serif;font-size:2.4rem;margin:1.6rem 0 .8rem 0;color:#000;text-transform:capitalize}.ax_page_subtitle{font-family:"Montserrat",sans-serif;font-size:1.8rem;color:#2a7a94}.ax_form p a{font-weight:600;color:#2a7a94;text-decoration:underline}.ax_form .ax_form_heading{width:100%;margin-bottom:1.6rem}.ax_form .ax_form_heading p{font-size:1.6rem;font-family:"Open Sans",sans-serif;margin-top:0}.ax_form .ax_field{display:flex;flex-direction:column;position:relative}.ax_form .ax_field h1,.ax_form .ax_field h2,.ax_form .ax_field h3,.ax_form .ax_field h4,.ax_form .ax_field h5,.ax_form .ax_field h6{font-family:"Montserrat",sans-serif;margin-top:0;color:#2a7a94}.ax_form .ax_field label{color:#2a7a94;font-family:"Montserrat",sans-serif;font-size:1.4rem;font-weight:700;display:block;width:100%;margin-bottom:.4rem}.ax_form .ax_field label span{color:red;font-weight:800;font-size:1.6rem;line-height:1.6rem}.ax_form .ax_field label p{margin-top:0}.ax_form .ax_field label .counter{width:100%;display:block}.ax_form .ax_field label .counter span{font-size:1rem;font-weight:500;font-family:"Montserrat",sans-serif}.ax_form .ax_field textarea,.ax_form .ax_field input[type=text],.ax_form .ax_field input[type=email],.ax_form .ax_field input[type=password],.ax_form .ax_field input[type=number],.ax_form .ax_field input[type=tel],.ax_form .ax_field input[type=date],.ax_form .ax_field select{width:100%;border-radius:.4rem;border:.1rem solid #d7dfe9;background:#f2f5f9;padding:0 4rem 0 1.6rem;color:#000;font-family:"Montserrat",sans-serif;font-size:1.4rem;display:inline-block;margin-bottom:1.6rem}.ax_form .ax_field input[type=text],.ax_form .ax_field input[type=email],.ax_form .ax_field input[type=password],.ax_form .ax_field input[type=number],.ax_form .ax_field input[type=tel],.ax_form .ax_field input[type=date],.ax_form .ax_field input[type=date],.ax_form .ax_field input[type=time],.ax_form .ax_field input[type=number],.ax_form .ax_field input[type=url],.ax_form .ax_field select{height:4rem}.ax_form .ax_field input[type=text]::-moz-placeholder, .ax_form .ax_field input[type=email]::-moz-placeholder, .ax_form .ax_field input[type=password]::-moz-placeholder, .ax_form .ax_field input[type=number]::-moz-placeholder, .ax_form .ax_field input[type=tel]::-moz-placeholder, .ax_form .ax_field input[type=date]::-moz-placeholder, .ax_form .ax_field input[type=date]::-moz-placeholder, .ax_form .ax_field input[type=time]::-moz-placeholder, .ax_form .ax_field input[type=number]::-moz-placeholder, .ax_form .ax_field input[type=url]::-moz-placeholder, .ax_form .ax_field select::-moz-placeholder{font-weight:400;color:#d7dfe9}.ax_form .ax_field input[type=text]::placeholder,.ax_form .ax_field input[type=email]::placeholder,.ax_form .ax_field input[type=password]::placeholder,.ax_form .ax_field input[type=number]::placeholder,.ax_form .ax_field input[type=tel]::placeholder,.ax_form .ax_field input[type=date]::placeholder,.ax_form .ax_field input[type=date]::placeholder,.ax_form .ax_field input[type=time]::placeholder,.ax_form .ax_field input[type=number]::placeholder,.ax_form .ax_field input[type=url]::placeholder,.ax_form .ax_field select::placeholder{font-weight:400;color:#d7dfe9}.ax_form .ax_field textarea{padding:1.6rem;min-height:16rem}.ax_form .ax_field button.see{position:absolute;top:2.2rem;right:0;width:4rem;height:4rem;border:none;background:rgba(0,0,0,0)}.ax_form .ax_field button.see svg{color:#414141}.ax_form .ax_field small{font-family:"Montserrat",sans-serif;font-size:1.4rem}.ax_form .ax_field input[type=checkbox]{margin-bottom:1.6rem;position:relative;-webkit-appearance:none;-moz-appearance:none;appearance:none}.ax_form .ax_field input[type=submit]{height:4rem;border-radius:.4rem;background:#3caba7;color:#fff;font-family:"Montserrat",sans-serif;font-size:1.6rem;font-weight:600;border:none}.ax_form input[type=checkbox]{-webkit-appearance:none;-moz-appearance:none;appearance:none;font-size:1.6rem;line-height:1.6rem;text-align:center;width:1.6rem;height:1.6rem;border-radius:.2rem;border:.2rem solid #d7dfe9;background:#f2f5f9;display:inline-block;position:relative;vertical-align:middle;margin-bottom:.4rem !important}.ax_form input[type=checkbox]:checked:before{content:"✓";font-size:1.6rem;line-height:1.6rem;text-align:center;width:1.2rem;height:1.2rem;border-radius:.2rem;background:rgba(0,0,0,0);border:none;display:block;position:absolute;left:0;top:0;z-index:50}.ax_field_with_icon{display:flex;flex-wrap:wrap;flex-direction:row;justify-content:flex-start}.ax_field_with_icon .iconInput{width:100%;display:block;position:relative}.ax_field_with_icon .iconInput>svg,.ax_field_with_icon .iconInput>img{position:absolute;top:.6rem;left:.6rem;z-index:10}.ax_field_with_icon h1,.ax_field_with_icon h2,.ax_field_with_icon h3,.ax_field_with_icon h4,.ax_field_with_icon h5,.ax_field_with_icon h6{font-family:"Montserrat",sans-serif;margin-top:0;color:#2a7a94}.ax_field_with_icon label{color:#2a7a94;font-family:"Montserrat",sans-serif;font-size:1.4rem;font-weight:700;display:block;width:100%;margin-bottom:.4rem}.ax_field_with_icon label span{color:red;font-weight:800;font-size:1.6rem}.ax_field_with_icon label p{margin-top:0}.ax_field_with_icon textarea,.ax_field_with_icon input[type=text],.ax_field_with_icon input[type=email],.ax_field_with_icon input[type=password],.ax_field_with_icon input[type=number],.ax_field_with_icon input[type=tel],.ax_field_with_icon input[type=date],.ax_field_with_icon select{width:100%;border-radius:.4rem;border:.1rem solid #d7dfe9;background:#f2f5f9;padding:0 4rem 0 3.8rem;color:#000;font-family:"Montserrat",sans-serif;font-size:1.4rem;display:inline-block;margin-bottom:1.6rem}.ax_field_with_icon input[type=text],.ax_field_with_icon input[type=email],.ax_field_with_icon input[type=password],.ax_field_with_icon input[type=number],.ax_field_with_icon input[type=tel],.ax_field_with_icon input[type=date],.ax_field_with_icon input[type=date],.ax_field_with_icon input[type=time],.ax_field_with_icon input[type=number],.ax_field_with_icon input[type=url],.ax_field_with_icon select{height:4rem}.ax_field_with_icon input[type=text]::-moz-placeholder, .ax_field_with_icon input[type=email]::-moz-placeholder, .ax_field_with_icon input[type=password]::-moz-placeholder, .ax_field_with_icon input[type=number]::-moz-placeholder, .ax_field_with_icon input[type=tel]::-moz-placeholder, .ax_field_with_icon input[type=date]::-moz-placeholder, .ax_field_with_icon input[type=date]::-moz-placeholder, .ax_field_with_icon input[type=time]::-moz-placeholder, .ax_field_with_icon input[type=number]::-moz-placeholder, .ax_field_with_icon input[type=url]::-moz-placeholder, .ax_field_with_icon select::-moz-placeholder{font-weight:400;color:#d7dfe9}.ax_field_with_icon input[type=text]::placeholder,.ax_field_with_icon input[type=email]::placeholder,.ax_field_with_icon input[type=password]::placeholder,.ax_field_with_icon input[type=number]::placeholder,.ax_field_with_icon input[type=tel]::placeholder,.ax_field_with_icon input[type=date]::placeholder,.ax_field_with_icon input[type=date]::placeholder,.ax_field_with_icon input[type=time]::placeholder,.ax_field_with_icon input[type=number]::placeholder,.ax_field_with_icon input[type=url]::placeholder,.ax_field_with_icon select::placeholder{font-weight:400;color:#d7dfe9}.ax_field_with_icon textarea{padding:1.6rem;min-height:16rem}.ax_field_with_icon button.see{position:absolute;top:2.2rem;right:0;width:4rem;height:4rem;border:none;background:rgba(0,0,0,0)}.ax_field_with_icon button.see svg{color:#414141}.ax_field_with_icon small{font-family:"Montserrat",sans-serif;font-size:1.4rem}.ax_field_with_icon input[type=checkbox]{margin-bottom:1.6rem}.ax_field_with_icon input[type=submit]{height:4rem;border-radius:.4rem;background:#3caba7;color:#fff;font-family:"Montserrat",sans-serif;font-size:1.6rem;font-weight:600;border:none}.inputDanger{border:.2rem solid #de1600 !important}.ax_datepicker{width:100%;height:4rem;border-radius:.4rem;border:.1rem solid #d7dfe9;background:#f2f5f9;padding:0 4rem 0 1.6rem;color:#000;font-family:"Montserrat",sans-serif;font-size:1.4rem;display:inline-block;margin-bottom:1.6rem}.ax_datepicker::-moz-placeholder{font-weight:400;color:#d7dfe9}.ax_datepicker::placeholder{font-weight:400;color:#d7dfe9}.ax_datepicker .datepickerToggle{display:inline-block;position:relative;width:18px;height:19px}.ax_datepicker .datepickerToggleButton{position:absolute;right:.8rem;top:.6rem;width:100%;height:100%;background:blue}.ax_datepicker .datepickerInput{position:absolute;left:0;top:0;width:100%;height:100%;opacity:0;cursor:pointer;box-sizing:border-box}.ax_datepicker .datepickerInput::-webkit-calendar-picker-indicator{position:absolute;left:0;top:0;width:100%;height:100%;margin:0;padding:0;cursor:pointer}.ax_card_list{width:100%;display:flex;flex-direction:row;justify-content:flex-start;flex-wrap:wrap}.boxedLoading{width:100%;height:100%;background:rgba(170,247,166,.8);display:flex;flex-direction:row;justify-content:center;align-items:center;position:absolute;left:0;top:0;z-index:500}.primary{color:#3caba7}.base{color:#000}.purple{color:#7020bc}.orange{color:#e2591b}.green{color:#8eb440}.yellow{color:#e6b800}.blue{color:#2d4bd2}.teal{color:#00a8a8}.img_responsive{width:100%;max-width:100%;height:auto}.textCenter{text-align:center}.textRight{text-align:right}.centerContent{width:100%;justify-content:center !important;align-items:center !important}.validation{padding:1.6rem;background:rgba(255,0,0,.1);border:.1rem solid rgba(255,0,0,.3);border-radius:.8rem;margin-bottom:3.2rem}.validation h3{font-family:"Montserrat",sans-serif;font-size:1.6rem;margin:0 0 1.6rem 0;color:#a13}.validation ul{list-style:none;padding:0;margin:0}.validation ul li{padding-left:.8rem;position:relative;color:#a13;font-family:"Montserrat",sans-serif;font-size:1.2rem;line-height:1.8rem;font-weight:600}.validation ul li:before{content:"";width:.4rem;height:.4rem;display:block;border-radius:50%;background:#a13;position:absolute;top:.6rem;left:0}.dragzone{width:100%;height:10rem;height:auto;display:block;position:relative;z-index:1}.dragzone span{width:100%;line-height:4.8rem;font-family:"Montserrat",sans-serif;color:#666;font-weight:700;pointer-events:none;position:absolute;top:28%;text-align:center;z-index:1;transition:all .2s ease-out}.dragzone .dragarea{width:100%;height:10rem;display:flex;justify-content:center;align-items:center;position:relative;background:#f2f5f9;border-radius:.8rem;border:.2rem dashed #d7dfe9;z-index:10;transition:all .2s ease-out;cursor:grab}.dragzone .dragarea:hover{background:#a6b3c4;cursor:grab}.dragzone .dragarea:hover span{color:#fff}.contentBox{padding:3.2rem;background:#fff;border-radius:.8rem;margin-bottom:3.2rem;width:100%;display:flex;flex-wrap:wrap}.contentBox h2,.contentBox h3,.contentBox h4,.contentBox h5,.contentBox h6{width:100%;display:block;font-family:"Montserrat",sans-serif;color:#000;margin:0 0 1.6rem 0}.contentBox h2{font-size:2.4rem;line-height:2.8rem}.contentBox h3{font-size:2.1rem;line-height:2.4rem}.contentBox h4{font-size:1.8rem;line-height:2.1rem}.contentBox h5{font-size:1.6rem;line-height:2.1rem}.contentBox h6{font-size:1.4rem;line-height:1.8rem}.contentBox p{font-family:"Open Sans",sans-serif;font-size:1.6rem;line-height:2.1rem;color:#000;margin:0 0 1.6rem 0}.contentBox p img{max-width:100%;height:auto;display:inline-block}.contentBox p a{color:#2a7a94;text-decoration:underline}.contentBox p a:hover{color:#3caba7}.contentBox p a img{max-width:100%;height:auto;display:inline-block}.contentBox img{max-width:100%;height:auto;display:inline-block}.contentBox ul{list-style:none;padding:0;margin-left:3.2rem;margin-bottom:3.2rem}.contentBox ul li{position:relative;padding-left:.8rem;display:block;font-family:"Open Sans",sans-serif;font-size:1.6rem;line-height:2.8rem}.contentBox ul li:before{content:"";width:.8rem;height:.8rem;display:block;border-radius:50%;position:absolute;top:1rem;left:-0.8rem;z-index:1;background-color:#aae2e0}.contentBox ol{padding:0;margin-left:3.2rem;margin-bottom:3.2rem}.contentBox ol li{position:relative;margin-left:.8rem;font-family:"Open Sans",sans-serif;font-size:1.6rem;line-height:2.8rem}.contentBox ol li::marker{color:#2a7a94}.contentBox pre{width:100%;background:#f2f5f9;border-radius:.8rem;overflow-x:scroll;padding:1.6rem;margin-bottom:3.2rem}.contentBox pre code{width:100%;display:table;font-family:monospace;font-size:1.4rem;line-height:1.8rem}.contentBox blockquote{border-left:.4rem solid #aae2e0;padding-left:1.6rem;font-family:"Montserrat",sans-serif;font-weight:600;margin:3.2rem 0 3.2rem 4rem}.contentBox blockquote p{color:#666}.contentBox blockquote p a{color:#2a7a94;text-decoration:underline}.sepparator{width:100%;height:.2rem;background:#d7dfe9;border-radius:.1rem;display:block}.forceParagraph{font-size:1.4rem !important;font-weight:400 !important;font-family:"Open Sans",sans-serif !important}.ax_tip{font-family:"Montserrat",sans-serif;border-left:.4rem solid #3caba7;background:#f2f5f9;color:#000;border-radius:0 .8rem .8rem 0;padding:.8rem}.ax_tip span{color:#3caba7}.ax_tip_error{font-family:"Montserrat",sans-serif;border-left:.4rem solid red;background:#f2f5f9;color:#540000;border-radius:0 .8rem .8rem 0;padding:.8rem}.ax_tip_error span{color:#3caba7}.alertBox{margin:auto;width:40rem;height:auto;border-radius:.8rem;background:#fff;grid-template-columns:37rem 3rem;grid-template-areas:"title close" "content content" "footer footer";position:relative;top:-15rem;display:grid}.alertBox h3{grid-area:title;width:calc(100% - 2rem);font-family:"Montserrat",sans-serif;font-size:1.6rem;margin:0;padding:1.6rem 0 0 1.6rem}.alertBox>button{width:2rem;height:2rem;grid-area:button;border:none;color:#3caba7;background:#d7dfe9;border-radius:50%;padding:0;cursor:pointer;grid-area:close;margin:.8rem 0 0 0;transition:all .2s ease-out}.alertBox>button svg{position:relative;top:.2rem;fill:#000}.alertBox>button:hover{background:red}.alertBox>button:hover svg{fill:#fff}.alertBox .content{grid-area:content;width:100%;padding:0 1.6rem}.alertBox .content p{font-family:"Open Sans",sans-serif;font-size:1.4rem;color:#000}.alertBox footer{grid-area:footer;display:flex;justify-content:space-between;align-items:center;height:5.6rem;padding:.8rem 1.6rem 1.6rem 1.6rem}.alertBox footer button{width:auto;background:#3caba7;color:#fff;height:3.4rem;margin-bottom:.8rem;font-family:"Montserrat",sans-serif;font-weight:600;padding:.8rem 2rem;border:none;border-radius:.4rem;cursor:pointer;transition:all .2s ease-out}.alertBox footer button:hover{background:#3ba9a5}.ax_hero{width:100%;height:25rem;background:#fff;border-radius:.8rem;padding:3.2rem;margin:1.6rem 0;position:relative;z-index:10}.ax_hero h1{margin:0}.ax_content{display:flex;flex-direction:row;justify-content:space-between;margin-top:1.6rem}.ax_content h1,.ax_content h2,.ax_content h3{font-family:"Montserrat",sans-serif}.ax_content h3{font-size:2.4rem;margin:0 0 1.6rem 0}.ax_content p{font-family:"Open Sans",sans-serif;font-size:1.4rem;line-height:1.8rem;margin:0 0 2.4rem 0}.ax_left_column{position:relative;width:25rem;display:block;padding:0;margin:0;z-index:50;overflow:hidden;border-radius:.8rem;background:#fff;align-self:flex-start}.ax_left_column h3{width:100%;font-size:1.6rem;color:#3caba7}.ax_left_column .photo{width:100%;display:block;position:relative}.ax_left_column .photo img{width:100%;max-width:100%;height:auto;border-radius:.8rem .8rem 0 0;background:#f2f5f9;display:block}.ax_left_column .photo .photoLoading{position:absolute;top:0;left:0;width:100%;display:table;text-align:center;background:#fff;border-bottom:.1rem solid #f2f5f9}.ax_left_column .photo .photoLoading img{width:80%;max-width:80%;height:auto}.ax_left_column .photo .photoLoading p{font-family:"Montserrat",sans-serif;font-size:1.6rem;color:#3caba7;margin:1.6rem 0;width:100%;display:block;text-align:center}.ax_left_column .ax_info{padding:1.6rem}.ax_left_column .ax_info h2,.ax_left_column .ax_info h3,.ax_left_column .ax_info h4,.ax_left_column .ax_info p{color:#000;font-family:"Montserrat",sans-serif;margin:0 0 .8rem 0;font-size:1.6rem}.ax_left_column .ax_info h2 span,.ax_left_column .ax_info h2 a,.ax_left_column .ax_info h3 span,.ax_left_column .ax_info h3 a,.ax_left_column .ax_info h4 span,.ax_left_column .ax_info h4 a,.ax_left_column .ax_info p span,.ax_left_column .ax_info p a{color:#3caba7}.ax_left_column .ax_info p{font-size:1.4rem}.ax_right_column{width:calc(100% - 28rem);display:flex;flex-direction:row;flex-wrap:wrap;justify-content:flex-start}.ax_right_column .content_left{width:100%;background:#fff;border-radius:.8rem;padding:3.2rem}.ax_right_column .content_left .ax_form{display:flex;flex-direction:row;flex-wrap:wrap;justify-content:space-between}.ax_right_column .content_left .ax_form .ax_field{width:calc(50% - 1.6rem)}@media screen and (min-width: 1200px){.ax_right_column .content_left .ax_form .ax_field{width:calc(50% - 1.6rem)}}@media screen and (min-width: 768px)and (max-width: 1199px){.ax_right_column .content_left .ax_form .ax_field{width:100%}}@media screen and (min-width: 0)and (max-width: 767px){.ax_right_column .content_left .ax_form .ax_field{width:100%}}.ax_form h1,.ax_form h2,.ax_form h3,.ax_form h4,.ax_form h5,.ax_form h6{font-family:"Montserrat",sans-serif}.ax_form p{font-family:"Open Sans",sans-serif;font-size:1.4rem;line-height:2.1rem;margin-top:0}.ax_form p .checkbox{font-weight:700}.ax_form p .checkbox:first-child{margin-right:1.6rem}.ax_form p .checkbox:last-child{margin-right:2.4rem}.ax_form .photo{width:100%;display:flex;justify-content:center;align-items:center;margin-bottom:1.6rem}.ax_form .photo svg{margin:3rem auto;fill:#d7dfe9}.phoneExt{display:flex;justify-content:space-between}.phoneExt div:nth-child(1){width:70%}.phoneExt div:nth-child(2){width:calc(30% - 1.6rem)}.ax_btn_submit{height:4rem;border-radius:.4rem;background:#3caba7;color:#fff;font-family:"Montserrat",sans-serif;font-size:1.6rem;font-weight:600;border:none;padding:0 2.4rem;float:right}.ax_btn_submit>img,.ax_btn_submit svg{display:inline-block;width:3.6rem !important;height:3.6rem !important;vertical-align:middle}.ax_image_options{font-family:"Montserrat",sans-serif;font-size:1.4rem;padding:.8rem;border-top:.1rem solid #f2f5f9}.ax_image_options h3{font-size:1.6rem;color:#3caba7}.ax_image_options input{width:100%;height:3.4rem;line-height:3.4rem;margin:1.6rem 0;position:relative;border-radius:.2rem;border:.4rem dashed #d7dfe9;background-color:#f2f5f9}.ax_image_options label{width:100%;display:block;font-size:1.4rem;line-height:1.4rem;color:#000;font-weight:600}.ax_image_options button{height:4rem;border-radius:.4rem;background:#3caba7;color:#fff;font-family:"Montserrat",sans-serif;font-size:1.6rem;font-weight:600;border:none;padding:0 2.4rem;width:100%;margin-top:.8rem}.ax_image_options button>img,.ax_image_options button svg{display:inline-block;vertical-align:middle}.badgeCard{width:auto;display:table;border-radius:.8rem;background:#fff}.photoForm input{width:100%;height:4rem}.photoForm button{width:100%;background:#3caba7;color:#fff;font-family:"Montserrat",sans-serif;font-weight:600;border:none;height:3.6rem;border-radius:.4rem;padding:.8rem 2.4rem;clear:both}.photoForm small{width:100%;display:block;margin-top:1.6rem}.sepparator{width:100%;height:.1rem;background:#f2f5f9;display:block;margin:1.6rem 0}.qrCode{width:100%;display:flex;justify-content:center;margin:1.6rem 0}@media screen and (min-width: 768px)and (max-width: 1199px){.ax_content{flex-wrap:wrap}.ax_content .ax_left_column{order:2}.ax_content .ax_right_column{order:1}}@media screen and (min-width: 0)and (max-width: 767px){.ax_content{flex-wrap:wrap}.ax_content .ax_left_column{width:100%;order:2}.ax_content .ax_right_column{width:100%;order:1;margin-bottom:1.6rem}}.qrCodes,.badges{width:100%;display:flex;flex-wrap:wrap;margin:3.2rem 0}.qrCodes h3,.badges h3{width:100%}.qrCodes .ax_card_list,.badges .ax_card_list{justify-content:flex-start !important}.qrCodes .ax_card_list .ax_card_body h3,.badges .ax_card_list .ax_card_body h3{word-wrap:wrap}.badges{padding:3.2rem;background:#fff;border-radius:.8rem}.qrCodes{padding:3.2rem;background:#fff;border-radius:.8rem}.qrCodes .ax_card_list .ax_card_body p,.qrCodes .ax_card_list .ax_card_body h3{word-break:break-word}.description ul{list-style:none;padding:0;font-family:"Montserrat",sans-serif;font-size:1.4rem}.description ul li{padding-left:1rem;position:relative;height:3.6rem;line-height:3.6rem}.description ul li::before{content:"";width:.6rem;height:.6rem;border-radius:50%;display:block;position:absolute;top:1.5rem;left:0;background:#3caba7}.validation{padding:1.6rem;background:rgba(255,0,0,.1);border:.1rem solid rgba(255,0,0,.3);border-radius:.8rem;margin-bottom:3.2rem}.validation h3{font-family:"Montserrat",sans-serif;font-size:1.6rem;margin:0 0 1.6rem 0;color:#a13}.validation ul{list-style:none;padding:0;margin:0}.validation ul li{padding-left:.8rem;position:relative;color:#a13;font-family:"Montserrat",sans-serif;font-size:1.2rem;line-height:1.8rem;font-weight:600}.validation ul li:before{content:"";width:.4rem;height:.4rem;display:block;border-radius:50%;background:#a13;position:absolute;top:.6rem;left:0}.sigContainer{display:table;border:none;width:500px;margin:0;padding:0;border:.1rem solid #d7dfe9}.initialsContainer{display:table;border:none;width:250px;margin:0;padding:0;border:.1rem solid #d7dfe9}.sigCanvas{width:100%;display:table}.formSectionTitle{width:100%;display:flex;flex-wrap:wrap;flex-direction:column;margin-top:1.6rem}.formSectionTitle h3{font-size:2.1rem;color:#000;font-family:"Montserrat",sans-serif;line-height:2.4rem;margin:0 0 .4rem 0;display:flex;flex-wrap:wrap;align-items:center;position:relative}.formSectionTitle h3 svg{color:#3caba7;margin-right:.8rem}.formSectionTitle h4{font-size:1.6rem;color:#000;font-family:"Montserrat",sans-serif;line-height:1.8rem;margin:.4rem 0 .8rem 0;display:block;color:#666}.formSectionTitle p{font-size:1.4rem;color:#000;font-family:"Open Sans",sans-serif;line-height:1.6rem;margin:.4rem 0 .8rem 0}.formSectionTitle .sepparator{width:100%;height:.2rem;display:block;background:#f2f5f9;margin:0 0 1.6rem 0}.counter{width:100%;display:flex;justify-content:flex-end}.counter span{font-size:1rem;font-weight:500;font-family:"Montserrat",sans-serif}.cropperModal{position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,.5);display:flex;justify-content:center;align-items:center;z-index:1000}.cropperContent{background-color:#fff;padding:20px;border-radius:8px;box-shadow:0 2px 10px rgba(0,0,0,.1);position:relative;width:90%;max-width:600px;max-height:90vh;overflow-y:auto;z-index:1001}.closeButton{position:absolute;top:12px;right:10px;background:none;border:none;font-size:24px;line-height:4rem;cursor:pointer;z-index:1002;background:#000;color:#fff;padding:0;border-radius:.4rem;width:3.2rem;height:3.2rem}.cropperContainer{display:flex;flex-direction:column;align-items:center;margin-bottom:20px}.cropperContainer .cropper{height:400px;width:100%;position:relative}.cropperContainer .controls{margin-top:20px;display:flex;flex-direction:column;align-items:center}.cropperContainer .controls label{margin-bottom:5px;font-family:"Montserrat",sans-serif;font-size:1.4rem;font-weight:600}.cropperContainer .zoomRange{width:100%;margin-bottom:20px}.cropperContainer .cropButton{width:100%;padding:1.2rem 1.6rem;background-color:#3caba7;color:#fff;border:none;border-radius:4px;font-family:"Montserrat",sans-serif;font-size:1.6rem;cursor:pointer}.cropperContainer .cropButton:hover{background-color:#000}/*# sourceMappingURL=Profile.module.min.css.map */