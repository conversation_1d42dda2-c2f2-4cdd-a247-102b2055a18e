@import 'theme';
@import 'mixins';

.brandList{
  width: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;

  .ax_card_list{
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;
    
    >div{
      margin: 0 1.6rem 1.6rem 0 ;
      align-self: stretch;
      >div{
        height: 100%;
      }
    }
  }

  @include responsive(desktop){
    .ax_card_list {
      > div {
        width: calc(25% - 1.6rem);
        display: flex;
        align-self: stretch;
      }
    }
  }
  
  @include responsive(tablet){
    .ax_card_list {
      > div {
        width: calc(33% - 1.6rem);
        display: block;
      }
    }
  }
  
  @include responsive(mobile){
    .ax_card_list {
      > div {
        width: 100%;
        display: block;
      }
    }
  }
}

.ax_colorBox {
  width: 30rem;
  background: #fff;
  border-radius: .8rem;
  display: flex;
  flex-direction: column;
  padding: 1.6rem;
  font-family: $display-font;
  box-shadow: $shadow;

  h2{
    margin: 0;
  }


  .title {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    font-size: 1.6rem;
    margin-bottom: 1.6rem;

    .color {
      width: 4rem;
      height: 4rem;
      border-radius: .4rem;
      display: block;
      margin-right: .8rem;
    }

    .green {
      background-color: $highlight-color;
    }

    .black {
      background-color: #000;
    }
  }

  
  .colorCode {
    font-family: $display-font;
    font-size: 1.4rem;
    font-weight: 600;
    color: $base-color;
    margin: .4rem 0;
    letter-spacing: .2rem;

    span {
      color: $highlight-color;
      letter-spacing: 0;
    }
  }
}

@include responsive(desktop){
  .ax_colorBox {
    width: calc(20% - 3.2rem);
    margin: 0 2.4rem 2.4rem 0;
  }
}

@include responsive(tablet){
  .ax_colorBox {
    width: calc(33% - 3.2rem);
    margin: 0 2.4rem 2.4rem 0;
  }
}

@include responsive(mobile){
  .ax_colorBox {
    width: 100%;
    margin: 0 0 2.4rem 0;
  }
}

.brandGuidelines{
  width: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: flex-start;
  background: $bright-color;
  border-radius: .8rem;
  box-shadow: $shadow;
  margin-bottom: 3.2rem;
  padding: 3.2rem;

  h2, h3{
    font-family: $display-font;    
    width: 100%;
    display: block;
    margin: 0 0 1.6rem 0;
  }

  h2{
    font-size: 2.4rem;
    line-height: 3.2rem;    
  }

  h3{
    font-size: 1.8rem;
    line-height: 2.4rem;    
  }



  p{
    width: 100%;
    display: block;
    margin: 0 0 3.2rem 0;
    font-family: $body-font;
    font-size: 1.4rem;
    line-height: 2.1rem;
  }
  
  > img{
    margin-bottom: 3.2rem;
  }

  .ax_card_list{
    display: flex;
    justify-content: flex-start;
  }

  .imagesList{
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;

    @include responsive(desktop){
      .image{
        width: 25rem;
        height: 25rem;
        display: block;
        margin: 0 3.2rem 0 0;

        h4{
          font-family: $display-font;
          font-size: 1.8rem;
          color: $highlight-dark;
        }

        img{
          width: 90%;
          max-width: 90%;
          height: auto;
        }
      }
    }

    
  }
}