
@import "https://fonts.googleapis.com/css2?family=Montserrat:wght@700&family=Open+Sans:ital,wght@0,400;0,700;1,400;1,700&display=swap";

@mixin responsive($screen) {
  @if $screen == mobile {
    @media screen and (min-width: 0) and (max-width: 767px){
      @content;
    }
  }@else if $screen == tablet {
    @media screen and (min-width: 768px) and (max-width: 1199px){
      @content;
    }
  }@else if $screen == desktop {
    @media screen and (min-width: 1200px) {
      @content;
    }
  }@else{
    @media screen and (min-width: 1200px) {
      @content;
    }
  }
}
 