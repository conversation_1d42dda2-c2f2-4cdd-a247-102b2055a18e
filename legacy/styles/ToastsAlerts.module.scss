@import 'theme';

.overlay {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 5000;
  background: rgba(0, 0, 0, 0.65);
  display: flex;
  justify-content: center;
  align-items: center;
}

.ax_toast {
  width: 40rem;
  height: auto;
  grid-template-columns: 34rem 4rem;
  grid-template-areas:
    'title button'
    'content content';
  padding: 1.6rem;
  border-radius: 0.8rem;
  background: white;
  box-shadow: $shadow-large;
  position: fixed;
  top: 8.8rem;
  right: 0.8rem;
  z-index: 500;
  transform: translateY(-30rem);
  opacity: 0;
  transition: all 0.5s ease-out;

  button {
    width: 3.2rem;
    height: 3.2rem;
    grid-area: button;
    border: none;
    color: $highlight-color;
    background: $bright-color;
    border-radius: 0.4rem;
    padding: 0;
    cursor: pointer;
  }

  h3 {
    grid-area: title;
    width: calc(100% - 1.6rem);
    font-family: $display-font;
    font-size: 1.6rem;
    margin: 0;
  }

  p {
    font-family: $body-font;
    font-size: 1.4rem;
    color: $base-color;
  }
}

.ax_toast_success {
  transform: translateY(0);
  opacity: 1;

  h3 {
    color: $highlight-color;
  }
}

.ax_toast_error {
  transform: translateY(0);
  opacity: 1;

  h3 {
    color: red;
  }
}

.ax_toast_hidden {
  display: none;
}

.ax_toast_visible {
  display: grid;
}

.ax_tip {
  font-family: $display-font;
  border-left: 0.4rem solid $highlight-color;
  background: $light-gray;
  color: $base-color;
  border-radius: 0 0.8rem 0.8rem 0;
  padding: 0.8rem;

  span {
    color: $highlight-color;
  }
}

.ax_tip_error {
  font-family: $display-font;
  border-left: 0.4rem solid red;
  background: $light-gray;
  color: rgb(84, 0, 0);
  border-radius: 0 0.8rem 0.8rem 0;
  padding: 0.8rem;

  span {
    color: $highlight-color;
  }
}

.alertBox {
  margin: auto;
  width: 40rem;
  height: auto;
  border-radius: 0.8rem;
  background: #fff;
  grid-template-columns: 37rem 3rem;
  grid-template-areas:
    'title close'
    'content content'
    'footer footer';
  position: relative;
  top: -15rem;
  display: grid;

  h3 {
    grid-area: title;
    width: calc(100% - 2rem);
    font-family: $display-font;
    font-size: 1.6rem;
    margin: 0;
    padding: 1.6rem 0 0 1.6rem;
  }

  > button {
    width: 2rem;
    height: 2rem;
    grid-area: button;
    border: none;
    color: $highlight-color;
    background: $mid-gray;
    border-radius: 50%;
    padding: 0;
    cursor: pointer;
    grid-area: close;
    margin: 0.8rem 0 0 0;
    transition: all 0.2s ease-out;
    svg {
      position: relative;
      top: 0.2rem;
      fill: #000;
    }

    &:hover {
      background: red;
      svg {
        fill: #fff;
      }
    }
  }

  .content {
    grid-area: content;
    width: 100%;
    padding: 0 1.6rem;

    p {
      font-family: $body-font;
      font-size: 1.4rem;
      color: $base-color;
    }
  }

  footer {
    grid-area: footer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 5.6rem;
    padding: 0.8rem 1.6rem 1.6rem 1.6rem;

    button {
      width: auto;
      background: $highlight-color;
      color: #fff;
      height: 3.4rem;
      margin-bottom: 0.8rem;
      font-family: $display-font;
      font-weight: 600;
      padding: 0.8rem 2rem;
      border: none;
      border-radius: 0.4rem;
      cursor: pointer;
      transition: all 0.2s ease-out;

      &:hover {
        background: darken($highlight-color, 0.5);
      }
    }
  }
}
